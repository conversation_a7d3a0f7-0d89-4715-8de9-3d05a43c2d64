package grabid

import (
	"context"
	"net/http"

	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
)

// GetPartnerInfoRequest define request param for get partner info
type GetPartnerInfoRequest struct {
	PartnerSafeID string `json:"partner_safe_id"`
}

// GetPartnerInfoResponse defines response for get partner info request
type GetPartnerInfoResponse struct {
	AppSafeID        string `json:"app_safe_id"`
	Name             string `json:"name"`
	AppPicURL        string `json:"app_pic_url"`
	Disabled         bool   `json:"disabled"`
	TokensValidAfter int64  `json:"tokens_valid_after"`
	Internal         bool   `json:"internal"`
}

var (
	getPartnerInfoDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "Get partner info",
		Method:  http.MethodGet,
		Path:    "/v1/partner/service/app",
	}
)

// GetPartnerInfo gets the partner info
func (gc *grabIDClientImpl) GetPartnerInfo(ctx context.Context, req *GetPartnerInfoRequest) (*GetPartnerInfoResponse, error) {
	slog.FromContext(ctx).Info(logTag, "executing get partner info request to grab id")

	headers := http.Header{
		AuthorizationHeaderKey: []string{gc.serviceID},
	}
	kResp := &GetPartnerInfoResponse{}

	ctx = klient.MakeContext(ctx, getPartnerInfoDescriptor)
	err := gc.machinery.RoundTrip(ctx, &external.KlientRequest{
		Ctx:            ctx,
		Descriptor:     getPartnerInfoDescriptor,
		RequestBody:    req,
		RequestHeaders: headers,
	}, &external.KlientResponse{ResponseBody: kResp})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to execute GetPartnerInfo", slog.Error(err))
		return nil, err
	}

	return kResp, nil
}
