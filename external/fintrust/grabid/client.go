package grabid

import (
	"context"
	"strings"
	"sync"

	"gitlab.com/gx-regional/dakota/common/tracing"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/klient/initialize"
)

const (
	service = "grab-id"
	logTag  = "external-identity"
)

const (
	AuthorizationHeaderKey = "Authorization"
	OAuth2CtxIDHeader      = "X-Grab-ID-OAUTH2-CTX-ID"
)

var (
	once   sync.Once
	client *grabIDClientImpl
	err    error
)

// GrabIDClient defines the interfaces grabIDClientImpl implements
//
//go:generate mockery --name=GrabIDClient --inpackage --case=underscore
type GrabIDClient interface {
	GetOAuth2ContextInfo(ctx context.Context, authCtx string) (*OAuth2ContextInfoResponse, error)
	Sign(ctx context.Context, req *SignRequest) (*SignResponse, error)
	InvalidateDeviceFromLoginSource(ctx context.Context, req *InvalidateDeviceRequest) error
	GetPartnerInfo(ctx context.Context, req *GetPartnerInfoRequest) (*GetPartnerInfoResponse, error)
}

type grabIDClientImpl struct {
	machinery klient.RoundTripper
	serviceID string
}

// NewGrabIDClient initialises a new client for grab id.
func NewGrabIDClient(config *external.ServiceConfig, tracer tracing.Tracer, opts ...klient.Option) (*grabIDClientImpl, error) {
	once.Do(func() {
		// For backwards compatibility
		if len(opts) == 0 {
			opts = []klient.Option{
				klient.WithServiceName(service),
				klient.WithTracing(tracer),
			}
		}

		roundTripper, errInit := initialize.New(config.BaseURL, opts...).Initialize()
		if errInit != nil {
			err = errInit
		}

		client = &grabIDClientImpl{
			machinery: roundTripper,
			serviceID: strings.Join([]string{"TOKEN", config.ServiceName, config.ServiceKey}, " "),
		}
	})

	if err != nil {
		return nil, err
	}
	return client, nil
}
