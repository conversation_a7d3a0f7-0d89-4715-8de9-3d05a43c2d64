package grabid

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/mock"

	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/klient/mocks"
)

func Test_InvalidateDeviceFromLoginSource(t *testing.T) {
	type args struct {
		ctx context.Context
		req *InvalidateDeviceRequest
	}
	type fields struct {
		machinery klient.RoundTripper
	}
	tests := []struct {
		name    string
		args    args
		fields  fields
		wantErr bool
	}{
		{
			name: "happy path",
			args: args{
				ctx: context.Background(),
			},
			fields: fields{
				machinery: func() klient.RoundTripper {
					mockClient := &mocks.RoundTripper{}
					mockClient.On("RoundTrip", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					return mockClient
				}(),
			},
		},
		{
			name: "error path",
			args: args{
				ctx: context.Background(),
			},
			fields: fields{
				machinery: func() klient.RoundTripper {
					mockClient := &mocks.RoundTripper{}
					mockClient.On("RoundTrip", mock.Anything, mock.Anything, mock.Anything).
						Return(errors.New("dummy-error")).
						Once()
					return mockClient
				}(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &grabIDClientImpl{
				machinery: tt.fields.machinery,
			}
			err := g.InvalidateDeviceFromLoginSource(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InvalidateDeviceFromLoginSource() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
