package grabid

import (
	"context"
	"fmt"
	"net/http"

	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
)

// OAuth2ContextInfoResponse defines response for oauth2 context info request
type OAuth2ContextInfoResponse struct {
	Claims        string `json:"claims"`
	PartnerID     string `json:"partner_id"`
	PartnerUserID string `json:"partner_user_id"`
}

var (
	getOAuth2ContextInfoDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "OAuth2ContextInfo",
		Method:  http.MethodGet,
		Path:    "/v1/oauth2/authz_info",
	}
)

// GetOAuth2ContextInfo gets the oAuth2 context info based on authz_ctx passed in
func (gc *grabIDClientImpl) GetOAuth2ContextInfo(ctx context.Context, authCtx string) (*OAuth2ContextInfoResponse, error) {
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("executing GetOAuth2ContextInfo request to grab id, authCtx = %s", authCtx))

	headers := http.Header{
		AuthorizationHeaderKey: []string{gc.serviceID},
		OAuth2CtxIDHeader:      []string{authCtx},
	}
	kResp := &OAuth2ContextInfoResponse{}
	ctx = klient.MakeContext(ctx, getOAuth2ContextInfoDescriptor)
	err := gc.machinery.RoundTrip(ctx, &external.KlientRequest{
		Ctx:            ctx,
		Descriptor:     getOAuth2ContextInfoDescriptor,
		RequestBody:    nil,
		RequestHeaders: headers,
	}, &external.KlientResponse{ResponseBody: kResp})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to execute GetOAuth2ContextInfo", slog.Error(err))
		return nil, err
	}

	return kResp, nil
}
