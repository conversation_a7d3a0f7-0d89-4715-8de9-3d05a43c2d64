package grabid

import (
	"context"
	"net/http"

	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
)

// SignRequest define request param for signing paysi TxID with Grab ID
type SignRequest struct {
	ContextID string `json:"ctx_id"`
	Scopes    string `json:"scopes"`
	AuthZCtx  string `json:"authz_ctx"`
}

// SignResponse define response from grab ID after paysi asked for sign
type SignResponse struct {
	ProofToken string `json:"proof_token"`
}

var (
	signDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "Sign",
		Method:  http.MethodPost,
		Path:    "/v1/oauth2/sign",
	}
)

// Sign ...
func (gc *grabIDClientImpl) Sign(ctx context.Context, req *SignRequest) (*SignResponse, error) {
	slog.FromContext(ctx).Info(logTag, "executing sign request to grab id")

	headers := http.Header{
		AuthorizationHeaderKey: []string{gc.serviceID},
	}
	kResp := &SignResponse{}

	ctx = klient.MakeContext(ctx, signDescriptor)
	err := gc.machinery.RoundTrip(ctx, &external.KlientRequest{
		Ctx:            ctx,
		Descriptor:     signDescriptor,
		RequestBody:    req,
		RequestHeaders: headers,
	}, &external.KlientResponse{ResponseBody: kResp})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to execute sign", slog.Error(err))
		return nil, err
	}

	return kResp, nil
}
