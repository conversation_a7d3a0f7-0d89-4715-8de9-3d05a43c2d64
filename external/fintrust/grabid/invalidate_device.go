package grabid

import (
	"context"
	"net/http"

	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
)

// InvalidateDeviceRequest define request param for invalidating a device
type InvalidateDeviceRequest struct {
	UserID      string `json:"userId"`
	LoginSource string `json:"loginSource"`
}

var (
	invalidateDeviceDescriptor = &klient.EndpointDescriptor{
		Service: service,
		Name:    "InvalidateDeviceFromLoginSource",
		Method:  http.MethodPost,
		Path:    "/v1/internal/invalidate-device",
	}
)

// InvalidateDeviceFromLoginSource ...
func (gc *grabIDClientImpl) InvalidateDeviceFromLoginSource(ctx context.Context, req *InvalidateDeviceRequest) error {
	slog.FromContext(ctx).Info(logTag, "executing invalidate device request to grab id")

	headers := http.Header{
		AuthorizationHeaderKey: []string{gc.serviceID},
	}

	ctx = klient.MakeContext(ctx, invalidateDeviceDescriptor)
	err := gc.machinery.RoundTrip(ctx, &external.KlientRequest{
		Ctx:            ctx,
		Descriptor:     invalidateDeviceDescriptor,
		RequestBody:    req,
		RequestHeaders: headers,
	}, &external.KlientResponse{ResponseBody: nil})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to execute invalidate device", slog.Error(err))
		return err
	}

	return nil
}
