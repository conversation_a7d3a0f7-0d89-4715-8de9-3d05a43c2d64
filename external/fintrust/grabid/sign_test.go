package grabid

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
	mocks "gitlab.myteksi.net/dakota/klient/mocks"
)

func Test_Sign(t *testing.T) {
	type args struct {
		ctx context.Context
		req *SignRequest
	}
	type fields struct {
		machinery klient.RoundTripper
	}
	tests := []struct {
		name    string
		args    args
		fields  fields
		want    *SignResponse
		wantErr bool
	}{
		{
			name: "happy path",
			args: args{
				ctx: context.Background(),
			},
			fields: fields{
				machinery: func() klient.RoundTripper {
					mockClient := &mocks.RoundTripper{}
					mockClient.On("RoundTrip", mock.Anything, mock.Anything, mock.Anything).
						Run(func(args mock.Arguments) {
							// Get the pointer to the KlientResponse from the third argument.
							respPtr := args.Get(2).(*external.KlientResponse)

							// Get the pointer to the kResp struct from the ResponseBody field.
							// This is the variable you want to set.
							kRespPtr := respPtr.ResponseBody.(*SignResponse)
							// Set the fields on the kResp pointer.
							*kRespPtr = SignResponse{
								ProofToken: "t",
							}
						}).
						Return(nil). // The RoundTrip method itself returns nil on success.
						Once()
					return mockClient
				}(),
			},
			want: &SignResponse{
				ProofToken: "t",
			},
		},
		{
			name: "error path",
			args: args{
				ctx: context.Background(),
			},
			fields: fields{
				machinery: func() klient.RoundTripper {
					mockClient := &mocks.RoundTripper{}
					mockClient.On("RoundTrip", mock.Anything, mock.Anything, mock.Anything).
						Return(errors.New("dummy-error")).
						Once()
					return mockClient
				}(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			g := &grabIDClientImpl{
				machinery: tt.fields.machinery,
			}
			got, err := g.Sign(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Sign() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Sign() = %v, want %v", got, tt.want)
			}
		})
	}
}
