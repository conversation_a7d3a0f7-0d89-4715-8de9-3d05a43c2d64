// Code generated by mockery v2.39.1. DO NOT EDIT.

package grabid

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockGrabIDClient is an autogenerated mock type for the GrabIDClient type
type MockGrabIDClient struct {
	mock.Mock
}

// GetOAuth2ContextInfo provides a mock function with given fields: ctx, authCtx
func (_m *MockGrabIDClient) GetOAuth2ContextInfo(ctx context.Context, authCtx string) (*OAuth2ContextInfoResponse, error) {
	ret := _m.Called(ctx, authCtx)

	if len(ret) == 0 {
		panic("no return value specified for GetOAuth2ContextInfo")
	}

	var r0 *OAuth2ContextInfoResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*OAuth2ContextInfoResponse, error)); ok {
		return rf(ctx, authCtx)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *OAuth2ContextInfoResponse); ok {
		r0 = rf(ctx, authCtx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*OAuth2ContextInfoResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, authCtx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPartnerInfo provides a mock function with given fields: ctx, req
func (_m *MockGrabIDClient) GetPartnerInfo(ctx context.Context, req *GetPartnerInfoRequest) (*GetPartnerInfoResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetPartnerInfo")
	}

	var r0 *GetPartnerInfoResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetPartnerInfoRequest) (*GetPartnerInfoResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetPartnerInfoRequest) *GetPartnerInfoResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*GetPartnerInfoResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetPartnerInfoRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// InvalidateDeviceFromLoginSource provides a mock function with given fields: ctx, req
func (_m *MockGrabIDClient) InvalidateDeviceFromLoginSource(ctx context.Context, req *InvalidateDeviceRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for InvalidateDeviceFromLoginSource")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *InvalidateDeviceRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Sign provides a mock function with given fields: ctx, req
func (_m *MockGrabIDClient) Sign(ctx context.Context, req *SignRequest) (*SignResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for Sign")
	}

	var r0 *SignResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *SignRequest) (*SignResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *SignRequest) *SignResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*SignResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *SignRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockGrabIDClient creates a new instance of MockGrabIDClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockGrabIDClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockGrabIDClient {
	mock := &MockGrabIDClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
