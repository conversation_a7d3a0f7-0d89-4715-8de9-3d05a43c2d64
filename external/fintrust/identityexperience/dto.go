package identityexperience

type CreateShellUserRequest struct {
	PhoneNumber string `json:"phoneNumber,omitempty"`
}

type CreateShellUserResponse struct {
	CustomerID string `json:"customerID,omitempty"`
}

type GetEmailDuplicateRequest struct {
	Email string `json:"email,omitempty"`
}

type GetEmailDuplicateResponse struct {
	IsDuplicate     bool  `json:"isDuplicate"`
	VerifiedAttempt int64 `json:"verifiedAttempt,omitempty"`
	MaxLimitAttempt int64 `json:"maxLimitAttempt,omitempty"`
}
