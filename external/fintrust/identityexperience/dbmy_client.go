package identityexperience

import (
	"context"
	"gitlab.com/gx-regional/dakota/common/tracing"
	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	api "gitlab.com/gx-regional/dbmy/identity-experience/api"
	"gitlab.com/gx-regional/dbmy/identity-experience/api/client"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
)

var (
	logTag = "external.identityexperience"
)

// DbmyClient - defines dbmy id experience client
type DbmyClient struct {
	identityExperienceClient api.Idexp
}

// NewDbmyClient instantiates a new Client.
func NewDbmyClient(conf *external.ServiceConfig, tracer tracing.Tracer) (*DbmyClient, error) {
	identityExperienceClient, err := client.NewIdexpClient(conf.BaseURL, klient.WithServiceName("id-exp"), klient.WithTracing(tracer))
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		identityExperienceClient: identityExperienceClient,
	}, nil
}

// NewDbmyClientWithOptions instantiates a new Client Options.
func NewDbmyClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (*DbmyClient, error) {
	identityExperienceClient, err := client.NewIdexpClient(conf.BaseURL, options...)
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		identityExperienceClient: identityExperienceClient,
	}, nil
}

func (c *DbmyClient) CreateShellUser(ctx context.Context, req *CreateShellUserRequest) (*CreateShellUserResponse, error) {
	apiResponse, err := c.identityExperienceClient.CreateShellUser(ctx,
		&api.CreateShellUserRequest{
			PhoneNumber: req.PhoneNumber,
		})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "IdentityExperienceClient.CreateShellUser error", slog.Error(err))
		return nil, err
	}
	response := &CreateShellUserResponse{
		CustomerID: apiResponse.SafeID,
	}
	return response, nil
}

func (c *DbmyClient) GetEmailDuplicate(ctx context.Context, req *GetEmailDuplicateRequest) (*GetEmailDuplicateResponse, error) {
	apiResponse, err := c.identityExperienceClient.GetEmailDuplicate(ctx,
		&api.GetEmailDuplicateRequest{
			Email: req.Email,
		})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "IdentityExperienceClient.GetEmailDuplicate error", slog.Error(err))
		return nil, err
	}
	response := &GetEmailDuplicateResponse{
		IsDuplicate:     apiResponse.IsDuplicate,
		VerifiedAttempt: apiResponse.VerifiedAttempt,
		MaxLimitAttempt: apiResponse.MaxLimitAttempt,
	}
	return response, nil
}
