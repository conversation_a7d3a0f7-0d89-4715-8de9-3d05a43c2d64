// Code generated by mockery v2.53.3. DO NOT EDIT.

package identityexperience

import (
	"context"
	mock "github.com/stretchr/testify/mock"
)

// MockIIdentityExperienceClient is an autogenerated mock type for the IIdentityExperienceClient type
type MockIIdentityExperienceClient struct {
	mock.Mock
}

// CreateShellUser provides a mock function with given fields: ctx, req
func (_m *MockIIdentityExperienceClient) CreateShellUser(ctx context.Context, req *CreateShellUserRequest) (*CreateShellUserResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateShellUser")
	}

	var r0 *CreateShellUserResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *CreateShellUserRequest) (*CreateShellUserResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *CreateShellUserRequest) *CreateShellUserResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CreateShellUserResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *CreateShellUserRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetEmailDuplicate provides a mock function with given fields: ctx, req
func (_m *MockIIdentityExperienceClient) GetEmailDuplicate(ctx context.Context, req *GetEmailDuplicateRequest) (*GetEmailDuplicateResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetEmailDuplicate")
	}

	var r0 *GetEmailDuplicateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetEmailDuplicateRequest) (*GetEmailDuplicateResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetEmailDuplicateRequest) *GetEmailDuplicateResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*GetEmailDuplicateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetEmailDuplicateRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockIIdentityExperienceClient creates a new instance of MockIIdentityExperienceClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockIIdentityExperienceClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockIIdentityExperienceClient {
	mock := &MockIIdentityExperienceClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
