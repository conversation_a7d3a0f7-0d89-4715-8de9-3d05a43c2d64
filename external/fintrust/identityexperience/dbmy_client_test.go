package identityexperience

import (
	"context"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/servus/v2"
	"testing"

	identityExperienceApi "gitlab.com/gx-regional/dbmy/identity-experience/api"
	identityExperienceMock "gitlab.com/gx-regional/dbmy/identity-experience/api/mock"
)

func Test_DbmyClient_CreateShellUser(t *testing.T) {
	// shared convenience
	var (
		mockIdentityExperienceClient = &identityExperienceMock.Idexp{}
		client                       = &DbmyClient{
			identityExperienceClient: mockIdentityExperienceClient,
		}
	)
	t.Run("happy path", func(t *testing.T) {
		var (
			req = &CreateShellUserRequest{
				PhoneNumber: "random phone number",
			}
			want = &CreateShellUserResponse{
				CustomerID: "safeID",
			}
		)

		mockIdentityExperienceClient.On("CreateShellUser", mock.Anything, mock.Anything).Return(&identityExperienceApi.CreateShellUserResponse{
			SafeID: "safeID",
		}, nil).Once()
		got, err := client.CreateShellUser(context.Background(), req)

		assert.NoError(t, err)
		assert.Equal(t, want, got)
	})

	t.Run("error path", func(t *testing.T) {
		var (
			req = &CreateShellUserRequest{
				PhoneNumber: "random phone number",
			}
			want = servus.InternalServerError("INTERNAL_SERVER_ERROR", "something went wrong")
		)

		mockIdentityExperienceClient.On("CreateShellUser", mock.Anything, mock.Anything).
			Return(nil, servus.InternalServerError("INTERNAL_SERVER_ERROR", "something went wrong")).Once()
		_, err := client.CreateShellUser(context.Background(), req)

		assert.EqualError(t, want, err.Error())
	})
}
