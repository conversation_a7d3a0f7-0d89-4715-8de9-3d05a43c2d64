// Package identityexperience provide the wrapper to return identity experience service client based on service group name from config .
package identityexperience

import (
	"context"
	"errors"

	"gitlab.com/gx-regional/dakota/common/tracing"
	"gitlab.com/gx-regional/dbmy/partner-integration/common"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
)

//go:generate mockery --name=IIdentityExperienceClient --inpackage --case=underscore
type IIdentityExperienceClient interface {
	CreateShellUser(ctx context.Context, req *CreateShellUserRequest) (*CreateShellUserResponse, error)
	GetEmailDuplicate(ctx context.Context, req *GetEmailDuplicateRequest) (*GetEmailDuplicateResponse, error)
}

// NewClient instantiates implementation of IIdentityExperienceClient based on service group name
func NewClient(conf *external.ServiceConfig, tracer tracing.Tracer) (IIdentityExperienceClient, error) {
	if conf.GroupName == common.GroupNameDBMY {
		return NewDbmyClient(conf, tracer)
	}
	return nil, errors.New("invalid value for identityExperience.groupName")
}

// NewClientWithOptions instantiates implementation of IIdentityExperienceClient based on api version and client options
func NewClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (IIdentityExperienceClient, error) {
	if conf.GroupName == common.GroupNameDBMY {
		return NewDbmyClientWithOptions(conf, options...)
	}

	return nil, errors.New("invalid value for identityExperience.groupName")
}
