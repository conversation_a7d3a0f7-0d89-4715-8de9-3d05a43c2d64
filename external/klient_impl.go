package external

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"

	_go "github.com/json-iterator/go"
	"gitlab.com/gx-regional/dakota/klient"
	"gitlab.com/gx-regional/dakota/klient/errorhandling"
	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
)

const (
	contentType     = "Content-Type"
	applicationJSON = "application/json"
	logTag          = "external"
)

type KlientRequest struct {
	Ctx            context.Context
	Descriptor     *klient.EndpointDescriptor
	RequestBody    interface{}
	RequestHeaders http.Header
}

// EncodeHTTPRequest implements klient.Request
func (req *KlientRequest) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	fullPath := fmt.Sprintf("%s%s", baseURL, req.Descriptor.Path)

	var body io.Reader
	if req.RequestBody != nil {
		jsonBytes, err := _go.Marshal(req.RequestBody)
		if err != nil {
			slog.FromContext(req.Ctx).Warn(logTag, "fail to marshal request body",
				slog.Error(err), tags.T("desc", req.Descriptor.Name), tags.T("reqBody", req.RequestBody))
			return nil, err
		}
		body = bytes.NewBuffer(jsonBytes)
	}

	httpReq, err := http.NewRequest(req.Descriptor.Method, fullPath, body)
	if err != nil {
		slog.FromContext(req.Ctx).Warn(logTag, "fail to create new HTTP request",
			slog.Error(err), tags.T("desc", req.Descriptor.Name))
		return nil, err
	}

	if req.RequestHeaders != nil {
		httpReq.Header = req.RequestHeaders
	}

	if body != nil {
		httpReq.Header.Add(contentType, applicationJSON)
	}

	return httpReq, nil
}

type KlientResponse struct {
	ResponseBody interface{}
}

// DecodeHTTPResponse implements klient.Response
func (resp *KlientResponse) DecodeHTTPResponse(res *http.Response) error {
	// for external clients, the error struct is unknown, should not try to unmarshall to Klient.Error
	if res.StatusCode >= 400 {
		respBody, _ := io.ReadAll(res.Body)
		return &errorhandling.Error{
			HTTPCode: res.StatusCode,
			Message:  string(respBody),
		}
	}

	err := _go.NewDecoder(res.Body).Decode(resp.ResponseBody)
	if err == io.EOF {
		// handle the case for empty response body
		return nil
	}
	return err
}
