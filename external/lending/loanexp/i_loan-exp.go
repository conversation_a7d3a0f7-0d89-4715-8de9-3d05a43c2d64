package loanexp

import (
	"context"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
)

//go:generate mockery --name=ILoanExpClient --inpackage --case=underscore
type ILoanExpClient interface {
	RegisterPaymentIntentForAssetAccount(ctx context.Context, req *RegisterPaymentIntentForAssetAccountRequest) (*RegisterPaymentIntentForAssetAccountResponse, error)
	CreateLoanDrawdown(ctx context.Context, req *CreateLoanDrawdownRequest) (*CreateLoanDrawdownResponse, error)
}

// NewClientWithOptions instantiates implementation of ILoanExpClient based on service group name
func NewClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (ILoanExpClient, error) {
	return NewDakotaClientWithOptions(conf, options...)
}
