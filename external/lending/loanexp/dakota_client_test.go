package loanexp

import (
	"context"
	"errors"

	"reflect"
	"testing"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/lending/loan-exp/api"
	mocks "gitlab.com/gx-regional/dakota/lending/loan-exp/api/mock"
)

func TestDakotaClient_RegisterPaymentIntentForAssetAccount(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *RegisterPaymentIntentForAssetAccountRequest
	}
	type fields struct {
		loanExpClient api.LoanExp
	}
	tests := []struct {
		name    string
		args    args
		fields  fields
		want    *RegisterPaymentIntentForAssetAccountResponse
		wantErr error
	}{
		{
			name: "happy path",
			args: args{
				ctx: context.Background(),
				request: &RegisterPaymentIntentForAssetAccountRequest{
					Amount: &Money{},
				},
			},
			fields: fields{
				loanExpClient: func() api.LoanExp {
					mockClient := &mocks.LoanExp{}
					mockClient.On("RegisterPaymentIntentForAssetAccount", mock.Anything, mock.Anything).Return(&api.RegisterPaymentIntentForAssetAccountResponse{}, nil)
					return mockClient
				}(),
			},
			want: &RegisterPaymentIntentForAssetAccountResponse{},
		},
		{
			name: "error path",
			args: args{
				ctx: context.Background(),
				request: &RegisterPaymentIntentForAssetAccountRequest{
					Amount: &Money{},
				},
			},
			fields: fields{
				loanExpClient: func() api.LoanExp {
					mockClient := &mocks.LoanExp{}
					mockClient.On("RegisterPaymentIntentForAssetAccount", mock.Anything, mock.Anything).Return(nil, errors.New("dummy-error"))
					return mockClient
				}(),
			},
			wantErr: errors.New("dummy-error"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &DakotaClient{
				loanExpClient: tt.fields.loanExpClient,
			}
			got, err := client.RegisterPaymentIntentForAssetAccount(tt.args.ctx, tt.args.request)
			if (err != nil) && (err.Error() != tt.wantErr.Error()) {
				t.Errorf("RegisterPaymentIntentForAssetAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RegisterPaymentIntentForAssetAccount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDakotaClient_CreateLoanDrawdown(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *CreateLoanDrawdownRequest
	}
	type fields struct {
		loanExpClient api.LoanExp
	}
	tests := []struct {
		name    string
		args    args
		fields  fields
		want    *CreateLoanDrawdownResponse
		wantErr error
	}{
		{
			name: "happy path",
			args: args{
				ctx: context.Background(),
				request: &CreateLoanDrawdownRequest{
					Principal: &Money{},
				},
			},
			fields: fields{
				loanExpClient: func() api.LoanExp {
					mockClient := &mocks.LoanExp{}
					mockClient.On("CreateLoanDrawdown", mock.Anything, mock.Anything).Return(&api.CreateLoanDrawdownResponse{
						Principal: &api.Money{},
					}, nil)
					return mockClient
				}(),
			},
			want: &CreateLoanDrawdownResponse{Principal: &Money{}},
		},
		{
			name: "error path",
			args: args{
				ctx: context.Background(),
				request: &CreateLoanDrawdownRequest{
					Principal: &Money{},
				},
			},
			fields: fields{
				loanExpClient: func() api.LoanExp {
					mockClient := &mocks.LoanExp{}
					mockClient.On("CreateLoanDrawdown", mock.Anything, mock.Anything).Return(nil, errors.New("dummy-error"))
					return mockClient
				}(),
			},
			wantErr: errors.New("dummy-error"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &DakotaClient{
				loanExpClient: tt.fields.loanExpClient,
			}
			got, err := client.CreateLoanDrawdown(tt.args.ctx, tt.args.request)
			if (err != nil) && (err.Error() != tt.wantErr.Error()) {
				t.Errorf("CreateLoanDrawdown() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateLoanDrawdown() = %v, want %v", got, tt.want)
			}
		})
	}
}
