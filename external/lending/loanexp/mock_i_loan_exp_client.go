// Code generated by mockery v2.53.3. DO NOT EDIT.

package loanexp

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockILoanExpClient is an autogenerated mock type for the ILoanExpClient type
type MockILoanExpClient struct {
	mock.Mock
}

// CreateLoanDrawdown provides a mock function with given fields: ctx, req
func (_m *MockILoanExpClient) CreateLoanDrawdown(ctx context.Context, req *CreateLoanDrawdownRequest) (*CreateLoanDrawdownResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateLoanDrawdown")
	}

	var r0 *CreateLoanDrawdownResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *CreateLoanDrawdownRequest) (*CreateLoanDrawdownResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *CreateLoanDrawdownRequest) *CreateLoanDrawdownResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CreateLoanDrawdownResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *CreateLoanDrawdownRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RegisterPaymentIntentForAssetAccount provides a mock function with given fields: ctx, req
func (_m *MockILoanExpClient) RegisterPaymentIntentForAssetAccount(ctx context.Context, req *RegisterPaymentIntentForAssetAccountRequest) (*RegisterPaymentIntentForAssetAccountResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for RegisterPaymentIntentForAssetAccount")
	}

	var r0 *RegisterPaymentIntentForAssetAccountResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *RegisterPaymentIntentForAssetAccountRequest) (*RegisterPaymentIntentForAssetAccountResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *RegisterPaymentIntentForAssetAccountRequest) *RegisterPaymentIntentForAssetAccountResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*RegisterPaymentIntentForAssetAccountResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *RegisterPaymentIntentForAssetAccountRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockILoanExpClient creates a new instance of MockILoanExpClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockILoanExpClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockILoanExpClient {
	mock := &MockILoanExpClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
