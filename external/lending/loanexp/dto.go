package loanexp

import "time"

type CommonHeaders struct {
	UserID    string
	ProfileID string
}

// RegisterPaymentIntentForAssetAccountRequest defines the register intent request
type RegisterPaymentIntentForAssetAccountRequest struct {
	CommonHeaders
	AccountID          string
	IdempotencyKey     string
	Amount             *Money
	Type               string
	ProductVariantCode string
}

// RegisterPaymentIntentForAssetAccountResponse defines the register intent response
type RegisterPaymentIntentForAssetAccountResponse struct {
	LoanTransactionID string
	IntentID          string
}

// CreateLoanDrawdownRequest define Request to create loan draw down
type CreateLoanDrawdownRequest struct {
	CommonHeaders
	ReferenceID                  string
	LoanTransactionID            string
	ProductVariantCode           string
	LoanName                     string
	ParentAccountID              string
	LoanTenorInMonths            int64
	Principal                    *Money
	DisbursalDate                string
	PreferredRepaymentDayOfMonth string
	CreatedBy                    string
	Metadata                     map[string]interface{}
	Type                         string
	AutoRepaymentEnabled         *bool
}

// CreateLoanDrawdownResponse define Response of loan draw down
type CreateLoanDrawdownResponse struct {
	ReferenceID                  string
	LoanTransactionID            string
	ProductVariantCode           string
	LoanName                     string
	ParentAccountID              string
	LoanTenorInMonths            int64
	Principal                    *Money
	DisbursalDate                string
	PreferredRepaymentDayOfMonth string
	CreatedBy                    string
	Metadata                     map[string]interface{}
	Type                         string
	Status                       string
	StatusReason                 string
	StatusDescription            string
	CreationTimestamp            time.Time
}

// Money Represents an amount of money with its currency type.
type Money struct {
	CurrencyCode string
	Val          int64
}
