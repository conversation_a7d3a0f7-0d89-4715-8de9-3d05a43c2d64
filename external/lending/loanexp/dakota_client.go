package loanexp

import (
	"context"

	"gitlab.com/gx-regional/dakota/common/tracing"
	"gitlab.com/gx-regional/dakota/lending/loan-exp/api"
	"gitlab.com/gx-regional/dakota/lending/loan-exp/api/client"
	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.com/gx-regional/dbmy/partner-integration/common/constant"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
)

const (
	logTag = "external.loanexp"
)

// DakotaClient - defines dbmy loan exp client
type DakotaClient struct {
	loanExpClient api.LoanExp
}

// NewDakotaClient instantiates a new Client.
func NewDakotaClient(conf *external.ServiceConfig, tracer tracing.Tracer) (*DakotaClient, error) {
	loanExpClient, err := client.NewLoanExpClient(conf.BaseURL, klient.WithServiceName("loan-exp"), klient.WithTracing(tracer))
	if err != nil {
		return nil, err
	}
	return &DakotaClient{
		loanExpClient: loanExpClient,
	}, nil
}

// NewDakotaClientWithOptions instantiates a new Client Options.
func NewDakotaClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (*DakotaClient, error) {
	loanExpClient, err := client.NewLoanExpClient(conf.BaseURL, options...)
	if err != nil {
		return nil, err
	}
	return &DakotaClient{
		loanExpClient: loanExpClient,
	}, nil
}

func (d *DakotaClient) RegisterPaymentIntentForAssetAccount(ctx context.Context, req *RegisterPaymentIntentForAssetAccountRequest) (*RegisterPaymentIntentForAssetAccountResponse, error) {
	ctx = external.AddCommonHeaders(ctx, constant.ServiceDigibank, req.UserID, req.ProfileID)
	slog.FromContext(ctx).Debug(logTag, "RegisterPaymentIntentForAssetAccount... ", slog.CustomTag("request", req))
	res, err := d.loanExpClient.RegisterPaymentIntentForAssetAccount(ctx, &api.RegisterPaymentIntentForAssetAccountRequest{
		AccountID:      req.AccountID,
		IdempotencyKey: req.IdempotencyKey,
		Amount: &api.Money{
			CurrencyCode: req.Amount.CurrencyCode,
			Val:          req.Amount.Val,
		},
		Type:               req.Type,
		ProductVariantCode: req.ProductVariantCode,
	})
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "RegisterPaymentIntentForAssetAccount err", slog.Error(err))
		return nil, err
	}
	return &RegisterPaymentIntentForAssetAccountResponse{
		LoanTransactionID: res.LoanTransactionID,
		IntentID:          res.IntentID,
	}, nil
}

func (d *DakotaClient) CreateLoanDrawdown(ctx context.Context, req *CreateLoanDrawdownRequest) (*CreateLoanDrawdownResponse, error) {
	ctx = external.AddCommonHeaders(ctx, constant.ServiceDigibank, req.UserID, req.ProfileID)
	slog.FromContext(ctx).Debug(logTag, "CreateLoanDrawdown... ", slog.CustomTag("request", req))
	res, err := d.loanExpClient.CreateLoanDrawdown(ctx, &api.CreateLoanDrawdownRequest{
		ReferenceID:        req.ReferenceID,
		LoanTransactionID:  req.LoanTransactionID,
		ProductVariantCode: req.ProductVariantCode,
		LoanName:           req.LoanName,
		ParentAccountID:    req.ParentAccountID,
		LoanTenorInMonths:  req.LoanTenorInMonths,
		Principal: &api.Money{
			CurrencyCode: req.Principal.CurrencyCode,
			Val:          req.Principal.Val,
		},
		DisbursalDate:                req.DisbursalDate,
		PreferredRepaymentDayOfMonth: req.PreferredRepaymentDayOfMonth,
		CreatedBy:                    req.CreatedBy,
		Metadata:                     req.Metadata,
		Type:                         req.Type,
		AutoRepaymentEnabled:         req.AutoRepaymentEnabled,
	})
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "CreateLoanDrawdown err", slog.Error(err))
		return nil, err
	}
	return &CreateLoanDrawdownResponse{
		ReferenceID:                  res.ReferenceID,
		LoanTransactionID:            res.LoanTransactionID,
		ProductVariantCode:           res.ProductVariantCode,
		LoanName:                     res.LoanName,
		ParentAccountID:              res.ParentAccountID,
		LoanTenorInMonths:            res.LoanTenorInMonths,
		Principal:                    &Money{Val: res.Principal.Val, CurrencyCode: res.Principal.CurrencyCode},
		DisbursalDate:                res.DisbursalDate,
		PreferredRepaymentDayOfMonth: res.PreferredRepaymentDayOfMonth,
		CreatedBy:                    res.CreatedBy,
		Metadata:                     res.Metadata,
		Type:                         res.Type,
		Status:                       res.Status,
		StatusReason:                 res.StatusReason,
		StatusDescription:            res.StatusDescription,
		CreationTimestamp:            res.CreationTimestamp,
	}, nil
}
