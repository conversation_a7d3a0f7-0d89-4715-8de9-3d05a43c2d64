package external

import "github.com/myteksi/hystrix-go/hystrix"

type ServicesConfig struct {
	AccountService     *ServiceConfig `json:"accountService"`
	IdentityExperience *ServiceConfig `json:"identityExperience"`
	GrabIDService      *ServiceConfig `json:"grabIDService"`
	CustomerMaster     *ServiceConfig `json:"customerMaster"`
	PartnerpayEngine   *ServiceConfig `json:"partnerpayEngine"`
	ApplicationService *ServiceConfig `json:"applicationService"`
	LoanExp            *ServiceConfig `json:"loanExp"`
	Hermes             *ServiceConfig `json:"hermes"`
}

// ServiceConfig defines config required to call dakota services.
type ServiceConfig struct {
	BaseURL                string                  `json:"baseURL"`
	GroupName              string                  `json:"groupName"`
	APIVersion             string                  `json:"apiVersion"`
	CircuitBreaker         *CBSetting              `json:"circuitBreaker"`
	EndpointLevelCBSetting *EndpointLevelCBSetting `json:"endpointCircuitBreaker"`

	// for grab-id
	ServiceKey  string `json:"serviceName"`
	ServiceName string `json:"serviceKey"`
}

// CBSetting stores circuit breaker configurations
type CBSetting struct {
	hystrix.CommandConfig
}

// EndpointLevelCBSetting ...
type EndpointLevelCBSetting struct {
	CircuitSetting  map[string]hystrix.CommandConfig `json:"circuitSetting"`
	IgnoredHTTPCode []int                            `json:"ignoredHTTPCode"`

	// Ignore4xxErrorCode config is only applied when IgnoredHTTPCode is empty
	Ignore4xxErrorCode bool `json:"ignore4xxErrorCode"`
}
