package clients

import (
	"gitlab.com/gx-regional/dakota/common/tracing"
	servus "gitlab.com/gx-regional/dakota/servus/v2"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/corebanking/accountservice"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/fintrust/grabid"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/fintrust/identityexperience"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/hermes"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/klientopts"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/lending/loanexp"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/onboarding/applicationservice"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/onboarding/customermaster"
	"gitlab.com/gx-regional/dbmy/partner-integration/external/payments/partnerpayengine"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// Register will register the configured client, i.e, `rppAdapter` will be registered as `client.rppAdapter`
func Register(app *servus.Application, requiredClients *[]string) {
	rc := external.NewClientsInitializer(
		map[external.ClientKey]interface{}{
			external.AccountServiceClientKey:     newAccountServiceClient,
			external.CustomerMasterClientKey:     newCustomerMasterClient,
			external.IdentityExperienceClientKey: newIdentityExperienceClient,
			external.GrabIDClientKey:             newGrabIDClient,
			external.PartnerpayEngineClientKey:   newPartnerpayEngineClient,
			external.ApplicationServiceClientKey: newApplicationServiceClient,
			external.LoanExpClientKey:            newLoanExpClient,
			external.HermesClientKey:             newHermesClient,
		})
	rc.Register(app, requiredClients)
}

func newAccountServiceClient(serviceConfig external.ServicesConfig, tracer tracing.Tracer, logger slog.YallLogger, app *servus.Application) (accountservice.IAccountService, error) {
	opts := klientopts.CreateKlientOptionsV2(
		string(servicename.AccountService),
		serviceConfig.AccountService.EndpointLevelCBSetting,
		tracer,
		logger,
		true,
		true,
		app.GetStatsD(),
		app.GetDogStatsD(),
	)

	return accountservice.NewClientWithOptions(serviceConfig.AccountService, opts...)
}

func newIdentityExperienceClient(serviceConfig external.ServicesConfig, tracer tracing.Tracer, logger slog.YallLogger, app *servus.Application) (identityexperience.IIdentityExperienceClient, error) {
	opts := klientopts.CreateKlientOptionsV2(
		string(servicename.Idexp),
		serviceConfig.IdentityExperience.EndpointLevelCBSetting,
		tracer,
		logger,
		true,
		true,
		app.GetStatsD(),
		app.GetDogStatsD(),
	)

	return identityexperience.NewDbmyClientWithOptions(serviceConfig.IdentityExperience, opts...)
}

func newGrabIDClient(serviceConfig external.ServicesConfig, tracer tracing.Tracer, logger slog.YallLogger, app *servus.Application) (grabid.GrabIDClient, error) {
	opts := klientopts.CreateKlientOptionsV2(
		"grab-id",
		serviceConfig.GrabIDService.EndpointLevelCBSetting,
		tracer,
		logger,
		true,
		true,
		app.GetStatsD(),
		app.GetDogStatsD(),
	)

	return grabid.NewGrabIDClient(serviceConfig.GrabIDService, nil, opts...)
}

func newCustomerMasterClient(serviceConfig external.ServicesConfig, tracer tracing.Tracer, logger slog.YallLogger, app *servus.Application) (customermaster.ICustomerMasterClient, error) {
	opts := klientopts.CreateKlientOptionsV2(
		string(servicename.CustomerMaster),
		serviceConfig.CustomerMaster.EndpointLevelCBSetting,
		tracer,
		logger,
		true,
		true,
		app.GetStatsD(),
		app.GetDogStatsD(),
	)

	return customermaster.NewDbmyClientWithOptions(serviceConfig.CustomerMaster, opts...)
}

func newPartnerpayEngineClient(serviceConfig external.ServicesConfig, tracer tracing.Tracer, logger slog.YallLogger, app *servus.Application) (partnerpayengine.IPartnerpayEngineClient, error) {
	opts := klientopts.CreateKlientOptionsV2(
		string(servicename.PartnerPayEngine),
		serviceConfig.PartnerpayEngine.EndpointLevelCBSetting,
		tracer,
		logger,
		true,
		true,
		app.GetStatsD(),
		app.GetDogStatsD(),
	)

	return partnerpayengine.NewDakotaClientWithOptions(serviceConfig.PartnerpayEngine, opts...)
}

func newApplicationServiceClient(serviceConfig external.ServicesConfig, tracer tracing.Tracer, logger slog.YallLogger, app *servus.Application) (applicationservice.IApplicationServiceClient, error) {
	opts := klientopts.CreateKlientOptionsV2(
		string(servicename.ApplicationService),
		serviceConfig.ApplicationService.EndpointLevelCBSetting,
		tracer,
		logger,
		true,
		true,
		app.GetStatsD(),
		app.GetDogStatsD(),
	)

	return applicationservice.NewDbmyClientWithOptions(serviceConfig.ApplicationService, opts...)
}

func newLoanExpClient(serviceConfig external.ServicesConfig, tracer tracing.Tracer, logger slog.YallLogger, app *servus.Application) (loanexp.ILoanExpClient, error) {
	opts := klientopts.CreateKlientOptionsV2(
		string(servicename.LoanExp),
		serviceConfig.LoanExp.EndpointLevelCBSetting,
		tracer,
		logger,
		true,
		true,
		app.GetStatsD(),
		app.GetDogStatsD(),
	)

	return loanexp.NewDakotaClientWithOptions(serviceConfig.LoanExp, opts...)
}
