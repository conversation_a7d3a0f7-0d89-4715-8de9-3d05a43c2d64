package external

import (
	"context"

	activeprofile "gitlab.com/gx-regional/dbmy/common/active-profile/v2"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
)

// AddCommonHeaders adds common headers to the context for external service calls
func AddCommonHeaders(ctx context.Context, serviceID, userID, profileID string) context.Context {
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, serviceID)
	if userID != "" {
		ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, userID)
	}
	if profileID != "" {
		ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXProfileID, profileID)
		ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXProfileType, string(activeprofile.GetProfileType(profileID)))
	}
	return ctx
}
