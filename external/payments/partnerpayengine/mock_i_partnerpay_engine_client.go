// Code generated by mockery v2.53.3. DO NOT EDIT.

package partnerpayengine

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockIPartnerpayEngineClient is an autogenerated mock type for the IPartnerpayEngineClient type
type MockIPartnerpayEngineClient struct {
	mock.Mock
}

// ExecuteIntent provides a mock function with given fields: ctx, req
func (_m *MockIPartnerpayEngineClient) ExecuteIntent(ctx context.Context, req *ExecuteIntentRequest) (*ExecuteIntentResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ExecuteIntent")
	}

	var r0 *ExecuteIntentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ExecuteIntentRequest) (*ExecuteIntentResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ExecuteIntentRequest) *ExecuteIntentResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ExecuteIntentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ExecuteIntentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateIntent provides a mock function with given fields: ctx, req
func (_m *MockIPartnerpayEngineClient) UpdateIntent(ctx context.Context, req *UpdateIntentRequest) (*UpdateIntentResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateIntent")
	}

	var r0 *UpdateIntentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *UpdateIntentRequest) (*UpdateIntentResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *UpdateIntentRequest) *UpdateIntentResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*UpdateIntentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *UpdateIntentRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockIPartnerpayEngineClient creates a new instance of MockIPartnerpayEngineClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockIPartnerpayEngineClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockIPartnerpayEngineClient {
	mock := &MockIPartnerpayEngineClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
