package partnerpayengine

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/payment/partnerpay-engine/api"
	mocks "gitlab.com/gx-regional/dakota/payment/partnerpay-engine/api/mock"
)

// help write unit tests for DbmyClient_UpdateIntent
func TestDbmyClient_UpdateIntent(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *UpdateIntentRequest
	}
	type fields struct {
		partnerpayEngineClient api.PartnerpayEngine
	}
	tests := []struct {
		name    string
		args    args
		fields  fields
		want    *UpdateIntentResponse
		wantErr error
	}{
		{
			name: "happy path",
			args: args{
				ctx: context.Background(),
				request: &UpdateIntentRequest{
					PaymentMethod: &UpdateIntentRequestPaymentMethod{},
				},
			},
			fields: fields{
				partnerpayEngineClient: func() api.PartnerpayEngine {
					mockClient := &mocks.PartnerpayEngine{}
					mockClient.On("UpdateIntent", mock.Anything, mock.Anything).Return(&api.UpdateIntentResponse{
						PaymentMethod: &api.UpdateIntentRequestPaymentMethod{},
					}, nil)
					return mockClient
				}(),
			},
			want: &UpdateIntentResponse{
				PaymentMethod: &UpdateIntentRequestPaymentMethod{},
			},
		},
		{
			name: "error path",
			args: args{
				ctx: context.Background(),
				request: &UpdateIntentRequest{
					PaymentMethod: &UpdateIntentRequestPaymentMethod{},
				},
			},
			fields: fields{
				partnerpayEngineClient: func() api.PartnerpayEngine {
					mockClient := &mocks.PartnerpayEngine{}
					mockClient.On("UpdateIntent", mock.Anything, mock.Anything).Return(&api.UpdateIntentResponse{
						PaymentMethod: &api.UpdateIntentRequestPaymentMethod{},
					}, errors.New("dummy-error"))
					return mockClient
				}(),
			},
			wantErr: errors.New("dummy-error"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &DakotaClient{
				partnerpayEngineClient: tt.fields.partnerpayEngineClient,
			}
			got, err := client.UpdateIntent(tt.args.ctx, tt.args.request)
			if (err != nil) && (err.Error() != tt.wantErr.Error()) {
				t.Errorf("UpdateIntent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateIntent() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDbmyClient_ExecuteIntent(t *testing.T) {
	type fields struct {
		partnerpayEngineClient api.PartnerpayEngine
	}
	type args struct {
		ctx context.Context
		req *ExecuteIntentRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ExecuteIntentResponse
		wantErr error
	}{
		{
			name: "happy path",
			args: args{
				ctx: context.Background(),
				req: &ExecuteIntentRequest{},
			},
			fields: fields{
				partnerpayEngineClient: func() api.PartnerpayEngine {
					mockClient := &mocks.PartnerpayEngine{}
					mockClient.On("ExecuteIntent", mock.Anything, mock.Anything).Return(&api.ExecuteIntentResponse{
						Account: &api.Account{},
						PaymentMethod: &api.PaymentMethod{
							Balance: &api.Balance{},
						},
					}, nil)
					return mockClient
				}(),
			},
			want: &ExecuteIntentResponse{
				Account: &Account{},
				PaymentMethod: &PaymentMethod{
					Balance: &Balance{},
				},
			},
		},
		{
			name: "error path",
			args: args{
				ctx: context.Background(),
				req: &ExecuteIntentRequest{},
			},
			fields: fields{
				partnerpayEngineClient: func() api.PartnerpayEngine {
					mockClient := &mocks.PartnerpayEngine{}
					mockClient.On("ExecuteIntent", mock.Anything, mock.Anything).Return(nil, errors.New("dummy-error"))
					return mockClient
				}(),
			},
			wantErr: errors.New("dummy-error"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := DakotaClient{
				partnerpayEngineClient: tt.fields.partnerpayEngineClient,
			}
			got, err := d.ExecuteIntent(tt.args.ctx, tt.args.req)
			if (err != nil) && (err.Error() != tt.wantErr.Error()) {
				t.Errorf("ExecuteIntent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ExecuteIntent() got = %v, want %v", got, tt.want)
			}
		})
	}
}
