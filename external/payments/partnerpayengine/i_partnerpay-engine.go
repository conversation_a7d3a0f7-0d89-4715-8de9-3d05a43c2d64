package partnerpayengine

import (
	"context"

	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
)

//go:generate mockery --name=IPartnerpayEngineClient --inpackage --case=underscore
type IPartnerpayEngineClient interface {
	UpdateIntent(ctx context.Context, req *UpdateIntentRequest) (*UpdateIntentResponse, error)
	ExecuteIntent(ctx context.Context, req *ExecuteIntentRequest) (*ExecuteIntentResponse, error)
}

// NewClientWithOptions instantiates implementation of IPartnerpayEngineClient based on service group name
func NewClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (IPartnerpayEngineClient, error) {
	return NewDakotaClientWithOptions(conf, options...)
}
