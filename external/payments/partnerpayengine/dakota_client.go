package partnerpayengine

import (
	"context"

	"gitlab.com/gx-regional/dakota/common/tracing"
	"gitlab.com/gx-regional/dakota/payment/partnerpay-engine/api"
	"gitlab.com/gx-regional/dakota/payment/partnerpay-engine/api/client"
	"gitlab.com/gx-regional/dbmy/partner-integration/common/constant"
	partnerpayengineCommon "gitlab.com/gx-regional/dbmy/partner-integration/common/partnerpayengine"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	logTag = "external.partnerpayengine"
)

// DakotaClient - defines dbmy partnerpay engine client
type DakotaClient struct {
	partnerpayEngineClient api.PartnerpayEngine
}

// NewDakotaClient instantiates a new Client.
func NewDakotaClient(conf *external.ServiceConfig, tracer tracing.Tracer) (*DakotaClient, error) {
	partnerpayEngineClient, err := client.NewPartnerpayEngineClient(conf.BaseURL, klient.WithServiceName("partnerpay-engine"), klient.WithTracing(tracer))
	if err != nil {
		return nil, err
	}
	return &DakotaClient{
		partnerpayEngineClient: partnerpayEngineClient,
	}, nil
}

// NewDakotaClientWithOptions instantiates a new Client Options.
func NewDakotaClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (*DakotaClient, error) {
	partnerpayEngineClient, err := client.NewPartnerpayEngineClient(conf.BaseURL, options...)
	if err != nil {
		return nil, err
	}
	return &DakotaClient{
		partnerpayEngineClient: partnerpayEngineClient,
	}, nil
}

func (d DakotaClient) UpdateIntent(ctx context.Context, req *UpdateIntentRequest) (*UpdateIntentResponse, error) {
	slog.FromContext(ctx).Info(logTag, "UpdateIntent...")
	ctx = external.AddCommonHeaders(ctx, constant.ServiceDigibank, req.CustomerID, req.ProfileID)
	updateIntentReq := &api.UpdateIntentRequest{
		IdempotencyKey: req.IdempotencyKey,
		IntentID:       req.IntentID,
		PaymentMethod: &api.UpdateIntentRequestPaymentMethod{
			Type:            api.PaymentType(req.PaymentMethod.Type),
			IdentifierType:  api.PaymentIdentifierType_ACCOUNT_NUMBER,
			IdentifierValue: req.PaymentMethod.IdentifierValue,
			SwiftCode:       req.PaymentMethod.SwiftCode,
			DisplayName:     req.PaymentMethod.DisplayName,
		},
	}
	slog.FromContext(ctx).Debug(logTag, "UpdateIntent... ", slog.CustomTag("request", updateIntentReq))
	res, err := d.partnerpayEngineClient.UpdateIntent(ctx, updateIntentReq)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "UpdateIntent err", slog.Error(err))
		return nil, err
	}

	return &UpdateIntentResponse{
		PaymentMethod: &UpdateIntentRequestPaymentMethod{
			Type:            partnerpayengineCommon.PaymentType(res.PaymentMethod.Type),
			IdentifierValue: res.PaymentMethod.IdentifierValue,
			SwiftCode:       res.PaymentMethod.SwiftCode,
			DisplayName:     res.PaymentMethod.DisplayName,
		},
	}, nil
}

func (d DakotaClient) ExecuteIntent(ctx context.Context, req *ExecuteIntentRequest) (*ExecuteIntentResponse, error) {
	slog.FromContext(ctx).Info(logTag, "ExecuteIntent...")
	ctx = external.AddCommonHeaders(ctx, constant.ServiceDigibank, req.CustomerID, req.ProfileID)
	executeIntentReq := &api.ExecuteIntentRequest{
		IdempotencyKey: req.IdempotencyKey,
		IntentID:       req.IntentID,
	}
	slog.FromContext(ctx).Debug(logTag, "ExecuteIntent... ", slog.CustomTag("request", executeIntentReq))
	res, err := d.partnerpayEngineClient.ExecuteIntent(ctx, executeIntentReq)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "ExecuteIntent err", slog.Error(err))
		return nil, err
	}

	return &ExecuteIntentResponse{
		IntentID:             res.IntentID,
		PaymentTransactionID: res.PaymentTransactionID,
		Status:               res.Status,
		Account: &Account{
			Number:      res.Account.Number,
			DisplayName: res.Account.DisplayName,
			SwiftCode:   res.Account.SwiftCode,
			PairingID:   res.Account.PairingID,
		},
		PaymentMethod: &PaymentMethod{
			Type:            partnerpayengineCommon.PaymentType(res.PaymentMethod.Type),
			IdentifierType:  partnerpayengineCommon.PaymentIdentifierType(res.PaymentMethod.IdentifierType),
			IdentifierValue: res.PaymentMethod.IdentifierValue,
			DisplayName:     res.PaymentMethod.DisplayName,
			Balance: &Balance{
				CurrencyCode: res.PaymentMethod.Balance.CurrencyCode,
				Val:          res.PaymentMethod.Balance.Val,
			},
			Status:                  partnerpayengineCommon.PaymentMethodStatus(res.PaymentMethod.Status),
			StatusReason:            res.PaymentMethod.StatusReason,
			StatusReasonDescription: res.PaymentMethod.StatusReasonDescription,
			SwiftCode:               res.PaymentMethod.SwiftCode,
		},
		Amount:   res.Amount,
		Currency: res.Currency,
		Type:     res.Type,
	}, nil
}
