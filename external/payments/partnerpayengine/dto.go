package partnerpayengine

import partnerpayengineCommon "gitlab.com/gx-regional/dbmy/partner-integration/common/partnerpayengine"

type UpdateIntentRequest struct {
	CustomerID     string
	ProfileID      string
	IdempotencyKey string
	IntentID       string
	PaymentMethod  *UpdateIntentRequestPaymentMethod
}

type UpdateIntentResponse struct {
	PaymentMethod *UpdateIntentRequestPaymentMethod
}

type UpdateIntentRequestPaymentMethod struct {
	Type            partnerpayengineCommon.PaymentType
	IdentifierValue string
	SwiftCode       string
	DisplayName     string
}

type ExecuteIntentRequest struct {
	CustomerID     string
	ProfileID      string
	IdempotencyKey string
	IntentID       string
}

type ExecuteIntentResponse struct {
	IntentID             string         `json:"intentID,omitempty"`
	PaymentTransactionID string         `json:"paymentTransactionID,omitempty"`
	PartnerTransactionID string         `json:"partnerTransactionID,omitempty"`
	Status               string         `json:"status,omitempty"`
	Account              *Account       `json:"account,omitempty"`
	PaymentMethod        *PaymentMethod `json:"paymentMethod,omitempty"`
	Amount               int64          `json:"amount,omitempty"`
	Currency             string         `json:"currency,omitempty"`
	Type                 string         `json:"type,omitempty"`
}

type Account struct {
	Number      string `json:"number,omitempty" validate:"string,max=36,required"`
	DisplayName string `json:"displayName,omitempty"`
	SwiftCode   string `json:"swiftCode,omitempty"`
	PairingID   string `json:"pairingID,omitempty"`
}
type PaymentMethod struct {
	Type                    partnerpayengineCommon.PaymentType           `json:"type,omitempty"`
	IdentifierType          partnerpayengineCommon.PaymentIdentifierType `json:"identifierType,omitempty"`
	IdentifierValue         string                                       `json:"identifierValue,omitempty"`
	DisplayName             string                                       `json:"displayName,omitempty"`
	Balance                 *Balance                                     `json:"balance,omitempty"`
	Status                  partnerpayengineCommon.PaymentMethodStatus   `json:"status,omitempty"`
	StatusReason            string                                       `json:"statusReason,omitempty"`
	StatusReasonDescription string                                       `json:"statusReasonDescription,omitempty"`
	SwiftCode               string                                       `json:"swiftCode,omitempty"`
}

type Balance struct {
	CurrencyCode string `json:"currencyCode,omitempty"`
	Val          int64  `json:"val"`
}
