package accountservice

import (
	"context"
	"errors"

	"gitlab.com/gx-regional/dakota/common/tracing"
	"gitlab.com/gx-regional/dbmy/partner-integration/common"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
)

//go:generate mockery --name IAccountService --inpackage --case=underscore
type IAccountService interface {
	GetAccountDetails(ctx context.Context, request *GetAccountDetailsRequest) (*GetAccountDetailsResponse, error)
	CheckPermissionsForAccount(ctx context.Context, request *CheckPermissionsForAccountRequest) (*CheckPermissionsForAccountResponse, error)
	ListAccounts(ctx context.Context, request *ListAccountsRequest) (*ListAccountsResponse, error)
	GetUserProfile(ctx context.Context, request *GetUserProfileRequest) (*GetUserProfileResponse, error)
}

// NewClient instantiates implementation of IAccountService based on api version
func NewClient(conf *external.ServiceConfig, tracer tracing.Tracer) (IAccountService, error) {
	if conf.GroupName == common.GroupNameDBMY {
		return NewDbmyClient(conf, tracer)
	}

	return nil, errors.New("invalid value for accountService.apiVersion")
}

// NewClientWithOptions instantiates implementation of IAccountService based on api version and client options
func NewClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (IAccountService, error) {
	if conf.GroupName == common.GroupNameDBMY {
		return NewDbmyClientWithOptions(conf, options...)
	}

	return nil, errors.New("invalid value for accountService.apiVersion")
}
