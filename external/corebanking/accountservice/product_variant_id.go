package accountservice

import (
	"errors"
	"strings"

	"gitlab.com/gx-regional/dbmy/partner-integration/common"
)

const (
	// SGMainAccountProductVariantID singapore main acc product variant id
	SGMainAccountProductVariantID ProductVariantID = "deposit_account"
	// MYMainAccountProductVariantID malaysia main acc product variant id
	MYMainAccountProductVariantID ProductVariantID = "DEPOSITS_ACCOUNT"
	// MYMainAccountProductVariantID malaysia DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT product variant id
	MYDefaultFlexiLoanAccountProductVarientID ProductVariantID = "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"
	// IDMainAccountProductVariantID indonesia main acc product variant id
	IDMainAccountProductVariantID ProductVariantID = "casa_account_default"
	// IDPocketAccountProductVariantID indonesia pocket acc product variant id
	IDPocketAccountProductVariantID ProductVariantID = "casa_pocket_default"
	// IDPocketOvoAccountProductVariantID indonesia pocket acc product variant id
	IDPocketOvoAccountProductVariantID ProductVariantID = "pocket_ovo"
	// BizOAProductVariantID SG and MY operating acc product variant id
	BizOAProductVariantID ProductVariantID = "BIZ_DEPOSIT_ACCOUNT"
)

var (
	all = map[ProductVariantID]string{
		SGMainAccountProductVariantID:             string(SGMainAccountProductVariantID),
		MYMainAccountProductVariantID:             string(MYMainAccountProductVariantID),
		MYDefaultFlexiLoanAccountProductVarientID: string(MYDefaultFlexiLoanAccountProductVarientID),
		IDMainAccountProductVariantID:             string(IDMainAccountProductVariantID),
		IDPocketAccountProductVariantID:           string(IDPocketAccountProductVariantID),
		IDPocketOvoAccountProductVariantID:        string(IDPocketOvoAccountProductVariantID),
		BizOAProductVariantID:                     string(BizOAProductVariantID),
	}

	// populated by init()
	allReverse map[string]ProductVariantID

	mainAccountProductVariantIDs = []ProductVariantID{
		SGMainAccountProductVariantID, MYMainAccountProductVariantID, IDMainAccountProductVariantID,
	}

	productVariantIDByCountryCodeMap = map[string][]ProductVariantID{
		common.CountryCodeSingapore: {SGMainAccountProductVariantID},
		common.CountryCodeIndonesia: {IDMainAccountProductVariantID, IDPocketAccountProductVariantID, IDPocketOvoAccountProductVariantID},
		common.CountryCodeMalaysia:  {MYMainAccountProductVariantID},
	}

	bizProductVariantIDByCountryCodeMap = map[string][]ProductVariantID{
		common.CountryCodeSingapore: {BizOAProductVariantID},
		common.CountryCodeMalaysia:  {BizOAProductVariantID},
	}

	// ErrUnrecognizedCountryCode err returned when country code is not exist on the LoanProductVariantIDMap
	ErrUnrecognizedCountryCode = errors.New("UNRECOGNIZED_COUNTRY_CODE")
	ErrInvalidProductVariantID = errors.New("INVALID_PRODUCT_VARIANT_ID")
)

// ProductVariantID product variant id of account
type ProductVariantID string

// Equal check equality of string value with product variant id type
func (p *ProductVariantID) Equal(value string) bool {
	return strings.EqualFold(string(*p), value)
}

// FindProductVariantIDByCountryCode return array of product variant id by country code
func FindProductVariantIDByCountryCode(countryCode string) ([]ProductVariantID, error) {
	variantIDs, exist := productVariantIDByCountryCodeMap[countryCode]
	if !exist {
		return nil, ErrUnrecognizedCountryCode
	} else {
		return variantIDs, nil
	}
}

// FindBizProductVariantIDByCountryCode return array of product variant id by country code
func FindBizProductVariantIDByCountryCode(countryCode string) ([]ProductVariantID, error) {
	variantIDs, exist := bizProductVariantIDByCountryCodeMap[countryCode]
	if !exist {
		return nil, ErrUnrecognizedCountryCode
	}
	return variantIDs, nil
}

// IsMainAccount check whether this product id is main account
func (p ProductVariantID) IsMainAccount() bool {
	for _, mainAccountProductVariantID := range mainAccountProductVariantIDs {
		if p == mainAccountProductVariantID {
			return true
		}
	}
	return false
}

// IsPocketAccount check whether this product id is pocket account
func (p ProductVariantID) IsPocketAccount() bool {
	return p == IDPocketAccountProductVariantID
}

//nolint:gochecknoinits
func init() {
	allReverse = make(map[string]ProductVariantID, len(all))
	for key, value := range all {
		allReverse[value] = key
	}
}

// GetMainAccountProductIDByCountry returns the main account product IDs for the given country code.
func GetMainAccountProductIDByCountry(countryCode string) (ProductVariantID, error) {
	variantIDs, err := FindProductVariantIDByCountryCode(countryCode)
	if err != nil {
		return "", err
	}

	var mainAccountIDs []ProductVariantID
	for _, variantID := range variantIDs {
		if variantID.IsMainAccount() {
			mainAccountIDs = append(mainAccountIDs, variantID)
		}
	}

	if len(mainAccountIDs) == 0 {
		return "", ErrInvalidProductVariantID
	}

	return mainAccountIDs[0], nil
}

// IsBizOperatingAccount check whether this product id is business main account
func (p ProductVariantID) IsBizOperatingAccount() bool {
	return p == BizOAProductVariantID
}
