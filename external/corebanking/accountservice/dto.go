package accountservice

import "time"

type GetAccountDetailsRequest struct {
	AccountID         string
	FetchBalance      bool
	AccountType       string
	ProductVariantIDs []string
}

type GetBalanceEnqueryRequest struct {
	AccountID  string
	UserSafeID string
}

type GetBalanceEnqueryresponse struct {
	AccountID           string        `json:"accountID,omitempty"`
	AccountName         string        `json:"accountName,omitempty"`
	AccountStatus       AccountStatus `json:"accountStatus,omitempty"`
	ApplicableHoldcodes []string      `json:"applicableHoldcodes,omitempty"`
	BalanceInfo         BalanceInfo   `json:"balanceInfo,omitempty"`
}

type GetAccountDetailsResponse struct {
	Account *Account `json:"account,omitempty"`
}

// Account - defines Account details
type Account struct {
	Id                        string            `json:"id,omitempty"`
	Name                      string            `json:"name,omitempty"`
	ParentAccountID           string            `json:"parentAccountID,omitempty"`
	CifNumber                 string            `json:"cifNumber,omitempty"`
	PermittedCurrencies       []string          `json:"permittedCurrencies,omitempty"`
	Status                    AccountStatus     `json:"status,omitempty"`
	ProductID                 string            `json:"productID,omitempty"`
	ProductVariantID          string            `json:"productVariantID,omitempty"`
	ProductSpecificParameters map[string]string `json:"productSpecificParameters,omitempty"`
	OpeningTimestamp          time.Time         `json:"openingTimestamp,omitempty"`
	AccountType               string            `json:"accountType,omitempty"`
	ProductVersionID          string            `json:"productVersionID,omitempty"`
	ClosingTimestamp          time.Time         `json:"closingTimestamp,omitempty"`
	InstanceParams            map[string]string `json:"instanceParams,omitempty"`
	DerivedParams             map[string]string `json:"derivedParams,omitempty"`
}

// Money - Represents an amount of money with its currency type.
type Money struct {
	CurrencyCode string `json:"currencyCode"`
	Val          int64  `json:"val"`
}

type AccountStatus string

type AccountBalance struct {
	AccountAddress string `json:"accountAddress,omitempty"`
	Phase          string `json:"phase,omitempty"`
	Amount         *Money `json:"amount,omitempty"`
}

type CASAAccountWithBalance struct {
	Id               string           `json:"id,omitempty"`
	ProductVariantID string           `json:"productVariantID,omitempty"`
	Status           AccountStatus    `json:"status,omitempty"`
	AvailableBalance *Money           `json:"availableBalance"`
	Balances         []AccountBalance `json:"balances,omitempty"`
}

type ListCASAAccountsForCustomerResponse struct {
	Accounts []CASAAccountWithBalance `json:"accounts,omitempty"`
}

const (
	AccountStatus_ACTIVE             AccountStatus = "ACTIVE"
	AccountStatus_DORMANT            AccountStatus = "DORMANT"
	AccountStatus_CLOSED             AccountStatus = "CLOSED"
	AccountStatus_PENDING_ACTIVATION AccountStatus = "PENDING_ACTIVATION"
)

type ProfileType string

const (
	ProfileRetail   ProfileType = "RETAIL"
	ProfileBusiness ProfileType = "BUSINESS"
)

type ApplicableHoldcode string

const (
	ApplicableHoldcode_KILL_SWITCH ApplicableHoldcode = "KILL_SWITCH"
)

type CheckPermissionsForAccountRequest struct {
	AccountID string `json:"accountID,omitempty"`
	CifNumber string `json:"cifNumber,omitempty"`
}

type CheckPermissionsForAccountResponse struct {
	Status AccountPermission `json:"status,omitempty"`
}

// AccountPermission ...
type AccountPermission string

// AccountPermission ...
const (
	AccountPermissionALLOWED   AccountPermission = "ALLOWED"
	AccountPermissionFORBIDDEN AccountPermission = "FORBIDDEN"
)

type ListAccountsRequest struct {
	CifNumber         string   `json:"cifNumber,omitempty"`
	ProductVariantIDs []string `json:"productVariantIds,omitempty"`
	CustomerType      string   `json:"customerType,omitempty"`
	FetchBalance      bool     `json:"fetchBalance,omitempty"`
}

type ListAccountsResponse struct {
	Accounts             []CustomerAccountDetail `json:"accounts,omitempty"`
	MaxChildAccountLimit int                     `json:"maxChildAccountLimit,omitempty"`
}

type CustomerAccountDetail struct {
	Id                        string            `json:"id,omitempty"`
	ParentAccountID           string            `json:"parentAccountID"`
	ProductVariantID          string            `json:"productVariantID,omitempty"`
	ProductVersionID          string            `json:"productVersionID,omitempty"`
	AccountType               string            `json:"accountType,omitempty"`
	AccountName               string            `json:"accountName,omitempty"`
	Status                    AccountStatus     `json:"status,omitempty"`
	OpeningTimestamp          time.Time         `json:"openingTimestamp,omitempty"`
	ApplicableHoldcodes       []string          `json:"applicableHoldcodes"`
	ProductSpecificParameters map[string]string `json:"productSpecificParameters"`
	Features                  *Feature          `json:"features,omitempty"`
	AvailableBalance          *Money            `json:"availableBalance"`
}
type BalanceInfo struct {
	Amount          *Money `json:"amount,omitempty"`
	AvailableAmount *Money `json:"availableAmount,omitempty"`
	HoldAmount      *Money `json:"holdAmount,omitempty"`
	AccruedTax      *Money `json:"accruedTax,omitempty"`
	AccruedInterest *Money `json:"accruedInterest,omitempty"`
}

type Feature struct {
	Credit bool `json:"credit,omitempty"`
	Debit  bool `json:"debit,omitempty"`
}

type GetCASAAccountSummaryRequest struct {
	AccountID string `json:"accountID,omitempty"`
	CifNumber string `json:"cifNumber,omitempty"`
}

type GetCASAAccountSummaryResponse struct {
	TotalBalance *Money `json:"totalBalance"`
}

type GetAccountBalanceRequest struct {
	AccountID string `json:"accountID,omitempty" validate:"regex,pattern=^[0-9]*$"`
}

type GetAccountBalanceResponse struct {
	AccountID        string           `json:"accountID,omitempty"`
	AvailableBalance *Money           `json:"availableBalance"`
	Balances         []AccountBalance `json:"balances,omitempty"`
}

type GetCASAAccountsBalanceSummaryRequest struct {
	AccountID string `json:"accountID,omitempty"`
}

type GetCASAAccountsBalanceSummaryResponse struct {
	TotalBalance       *Money `json:"totalBalance,omitempty"`
	TotalLedgerBalance *Money `json:"totalLedgerBalance,omitempty"`
}

type BalanceSummary struct {
	AvailableBalance *Money `json:"availableBalance,omitempty"`
	LedgerBalance    *Money `json:"ledgerBalance,omitempty"`
}

type PocketBalanceSummary struct {
	TotalBalance  *Money          `json:"totalBalance,omitempty"`
	SavingsPocket *BalanceSummary `json:"savingsPocket,omitempty"`
	BoostPocket   *BalanceSummary `json:"boostPocket,omitempty"`
}

type ListAccountsForCustomerRequest struct {
	CifNumber    string `json:"cifNumber,omitempty"`
	FetchBalance bool   `json:"fetchBalance,omitempty"`
}

type ListAccountsForCustomerResponse struct {
	Accounts             []CustomerAccountDetail `json:"accounts,omitempty"`
	MaxChildAccountLimit int64                   `json:"maxChildAccountLimit,omitempty"`
}

type GetUserPublicIDRequest struct {
	SafeID    string
	AccountID string
}

type GetUserPublicIDResponse struct {
	PublicID string
}

type GetUserProfileRequest struct {
	SafeID    string `json:"safeID"`
	AccountID string `json:"accountID"`
}

type GetUserProfileResponse struct {
	ID   string      `json:"id"`
	Type ProfileType `json:"type"`
}
