package accountservice

import (
	"reflect"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestProductVariantID_Equal(t *testing.T) {
	type args struct {
		value string
	}
	tests := []struct {
		name             string
		args             args
		productVariantID ProductVariantID
		want             bool
	}{
		{
			name:             "happy-path: equality check for indonesia product variant id",
			productVariantID: IDMainAccountProductVariantID,
			args: args{
				value: "casa_account_default",
			},
			want: true,
		},
		{
			name:             "happy-path: equality check for indonesia pocket product variant id",
			productVariantID: IDPocketAccountProductVariantID,
			args: args{
				value: "casa_pocket_default",
			},
			want: true,
		},
		{
			name:             "happy-path: equality check for indonesia pocket ovo product variant id",
			productVariantID: IDPocketOvoAccountProductVariantID,
			args: args{
				value: "pocket_ovo",
			},
			want: true,
		},
		{
			name:             "happy-path: equality check for malaysia product variant id",
			productVariantID: MYMainAccountProductVariantID,
			args: args{
				value: "DEPOSITS_ACCOUNT",
			},
			want: true,
		},
		{
			name:             "happy-path: equality check for singapore product variant id",
			productVariantID: SGMainAccountProductVariantID,
			args: args{
				value: "deposit_account",
			},
			want: true,
		},
	}
	for _, tt := range tests {
		scenario := tt
		t.Run(scenario.name, func(t *testing.T) {
			assert.Equal(t, scenario.want, scenario.productVariantID.Equal(scenario.args.value))
		})
	}
}

func TestGetMainAccountProductIDByCountry(t *testing.T) {
	testCases := []struct {
		countryCode string
		expected    ProductVariantID
		wantErr     bool
	}{
		{"SG", SGMainAccountProductVariantID, false},
		{"MY", MYMainAccountProductVariantID, false},
		{"ID", IDMainAccountProductVariantID, false},
		{"dummy-country", "", true},
	}

	for _, tc := range testCases {
		t.Run(tc.countryCode, func(t *testing.T) {
			actual, err := GetMainAccountProductIDByCountry(tc.countryCode)
			if (err != nil) != tc.wantErr {
				t.Errorf("GetMainAccountProductIDByCountry(%s) error = %v, wantErr %v", tc.countryCode, err, tc.wantErr)
				return
			}
			if actual != tc.expected {
				t.Errorf("GetMainAccountProductIDByCountry(%s) = %s, want %s", tc.countryCode, actual, tc.expected)
			}
		})
	}
}

func TestFindBizProductVariantIDByCountryCode(t *testing.T) {
	testCases := []struct {
		countryCode string
		expected    []ProductVariantID
		wantErr     bool
	}{
		{"SG", []ProductVariantID{BizOAProductVariantID}, false},
		{"MY", []ProductVariantID{BizOAProductVariantID}, false},
		{"dummy-country", nil, true},
	}

	for _, tc := range testCases {
		t.Run(tc.countryCode, func(t *testing.T) {
			actual, err := FindBizProductVariantIDByCountryCode(tc.countryCode)
			if (err != nil) != tc.wantErr {
				t.Errorf("FindBizProductVariantIDByCountryCode(%s) error = %v, wantErr %v", tc.countryCode, err, tc.wantErr)
				return
			}
			if !reflect.DeepEqual(actual, tc.expected) {
				t.Errorf("FindBizProductVariantIDByCountryCode(%s) = %s, want %s", tc.countryCode, actual, tc.expected)
			}
		})
	}
}

func Test_IsPocketAccount(t *testing.T) {
	testCases := []struct {
		productVariantID ProductVariantID
		expected         bool
	}{
		{MYMainAccountProductVariantID, false},
		{IDPocketAccountProductVariantID, true},
	}

	for i, tc := range testCases {
		t.Run(strconv.Itoa(i), func(t *testing.T) {
			actual := tc.productVariantID.IsPocketAccount()
			if actual != tc.expected {
				t.Errorf("IsPocketAccount() = %t, want %t", actual, tc.expected)
			}
		})
	}
}

func Test_IsBizOperatingAccount(t *testing.T) {
	testCases := []struct {
		productVariantID ProductVariantID
		expected         bool
	}{
		{MYMainAccountProductVariantID, false},
		{BizOAProductVariantID, true},
	}

	for i, tc := range testCases {
		t.Run(strconv.Itoa(i), func(t *testing.T) {
			actual := tc.productVariantID.IsBizOperatingAccount()
			if actual != tc.expected {
				t.Errorf("IsBizOperatingAccount() = %t, want %t", actual, tc.expected)
			}
		})
	}
}
