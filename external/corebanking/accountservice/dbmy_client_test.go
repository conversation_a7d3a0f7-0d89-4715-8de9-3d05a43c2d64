package accountservice

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/stretchr/testify/mock"

	commonCtx "gitlab.com/gx-regional/dakota/common/context"
	"gitlab.com/gx-regional/dbmy/core-banking/account-service/api"
	mocks "gitlab.com/gx-regional/dbmy/core-banking/account-service/api/mock"
)

func Test_DbmyClient_GetAccountDetails(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *GetAccountDetailsRequest
	}
	type fields struct {
		accountServiceClient api.AccountService
	}
	tests := []struct {
		name    string
		args    args
		fields  fields
		want    *GetAccountDetailsResponse
		wantErr bool
	}{
		{
			name: "happy path",
			args: args{
				ctx:     context.Background(),
				request: &GetAccountDetailsRequest{},
			},
			fields: fields{
				accountServiceClient: func() api.AccountService {
					mockClient := &mocks.AccountService{}
					mockClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&api.GetAccountResponse{
						Account: &api.Account{Id: "999", CifNumber: "MY1234", Status: api.AccountStatus_ACTIVE},
					}, nil)

					return mockClient
				}(),
			},
			want: &GetAccountDetailsResponse{
				Account: &Account{
					Id:        "999",
					CifNumber: "MY1234",
					Status:    AccountStatus_ACTIVE,
				},
			},
		},
		{
			name: "error path",
			args: args{
				ctx:     context.Background(),
				request: &GetAccountDetailsRequest{},
			},
			fields: fields{
				accountServiceClient: func() api.AccountService {
					mockClient := &mocks.AccountService{}
					mockClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(nil, errors.New("dummy-error"))
					return mockClient
				}(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &DbmyClient{
				accountServiceClient: tt.fields.accountServiceClient,
			}
			got, err := client.GetAccountDetails(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAccountDetails() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_DbmyClient_CheckPermissionsForAccount(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *CheckPermissionsForAccountRequest
	}
	type fields struct {
		accountServiceClient api.AccountService
	}
	tests := []struct {
		name    string
		args    args
		fields  fields
		want    *CheckPermissionsForAccountResponse
		wantErr bool
	}{
		{
			name: "happy path - allowed",
			args: args{
				ctx:     context.Background(),
				request: &CheckPermissionsForAccountRequest{},
			},
			fields: fields{
				accountServiceClient: func() api.AccountService {
					mockClient := &mocks.AccountService{}
					mockClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(&api.CheckPermissionsForAccountResponse{
						Status: api.AccountPermission_ALLOWED,
					}, nil)
					return mockClient
				}(),
			},
			want: &CheckPermissionsForAccountResponse{
				Status: AccountPermissionALLOWED,
			},
		},
		{
			name: "happy path - forbidden",
			args: args{
				ctx:     context.Background(),
				request: &CheckPermissionsForAccountRequest{},
			},
			fields: fields{
				accountServiceClient: func() api.AccountService {
					mockClient := &mocks.AccountService{}
					mockClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(&api.CheckPermissionsForAccountResponse{
						Status: api.AccountPermission_FORBIDDEN,
					}, nil)
					return mockClient
				}(),
			},
			want: &CheckPermissionsForAccountResponse{
				Status: AccountPermissionFORBIDDEN,
			},
		},
		{
			name: "error path",
			args: args{
				ctx:     context.Background(),
				request: &CheckPermissionsForAccountRequest{},
			},
			fields: fields{
				accountServiceClient: func() api.AccountService {
					mockClient := &mocks.AccountService{}
					mockClient.On("CheckPermissionsForAccount", mock.Anything, mock.Anything).Return(nil, errors.New("dummy-error"))
					return mockClient
				}(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &DbmyClient{
				accountServiceClient: tt.fields.accountServiceClient,
			}
			got, err := client.CheckPermissionsForAccount(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckPermissionsForAccount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckPermissionsForAccount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_DbmyClient_ListAccounts(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *ListAccountsRequest
	}
	type fields struct {
		accountServiceClient api.AccountService
	}
	tests := []struct {
		name    string
		args    args
		fields  fields
		want    *ListAccountsResponse
		wantErr bool
	}{
		{
			name: "happy path",
			args: args{
				ctx:     context.Background(),
				request: &ListAccountsRequest{},
			},
			fields: fields{
				accountServiceClient: func() api.AccountService {
					mockClient := &mocks.AccountService{}
					mockClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(&api.ListCASAAccountsForCustomerDetailResponse{
						Accounts: []api.CASAAccountDetail{
							{
								Id:     "1",
								Status: api.AccountStatus_ACTIVE,
								AvailableBalance: &api.Money{
									CurrencyCode: "MYR",
									Val:          100,
								}},
						},
					}, nil)
					return mockClient
				}(),
			},
			want: &ListAccountsResponse{
				Accounts: []CustomerAccountDetail{
					{
						Id:     "1",
						Status: AccountStatus_ACTIVE,
						AvailableBalance: &Money{
							CurrencyCode: "MYR",
							Val:          100,
						},
						Features: v2CASAAcctToCstFeature(nil)},
				},
			},
		},
		{
			name: "error path",
			args: args{
				ctx:     context.Background(),
				request: &ListAccountsRequest{},
			},
			fields: fields{
				accountServiceClient: func() api.AccountService {
					mockClient := &mocks.AccountService{}
					mockClient.On("ListCASAAccountsForCustomerDetail", mock.Anything, mock.Anything).Return(nil, errors.New("dummy-error"))
					return mockClient
				}(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &DbmyClient{
				accountServiceClient: tt.fields.accountServiceClient,
			}
			got, err := client.ListAccounts(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ListAccounts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ListAccounts() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_DbmyClient_GetUserPublicID(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *GetUserProfileRequest
	}
	type fields struct {
		accountServiceClient api.AccountService
	}
	tests := []struct {
		name    string
		args    args
		fields  fields
		want    *GetUserProfileResponse
		wantErr bool
	}{
		{
			name: "happy path - retail customer",
			args: args{
				ctx:     context.Background(),
				request: &GetUserProfileRequest{},
			},
			fields: fields{
				accountServiceClient: func() api.AccountService {
					mockClient := &mocks.AccountService{}
					mockClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&api.GetAccountResponse{
						Account: &api.Account{
							CifNumber: "cif",
						},
					}, nil)
					return mockClient
				}(),
			},
			want: &GetUserProfileResponse{
				ID:   "cif",
				Type: ProfileRetail,
			},
		},
		{
			name: "happy path - msme customer",
			args: args{
				ctx:     context.Background(),
				request: &GetUserProfileRequest{},
			},
			fields: fields{
				accountServiceClient: func() api.AccountService {
					mockClient := &mocks.AccountService{}
					mockClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&api.GetAccountResponse{
						Account: &api.Account{
							CifNumber: "BIFxxx",
						},
					}, nil)
					return mockClient
				}(),
			},
			want: &GetUserProfileResponse{
				ID:   "BIFxxx",
				Type: ProfileBusiness,
			},
		},
		{
			name: "error path",
			args: args{
				ctx:     context.Background(),
				request: &GetUserProfileRequest{},
			},
			fields: fields{
				accountServiceClient: func() api.AccountService {
					mockClient := &mocks.AccountService{}
					mockClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(nil, errors.New("dummy-error"))
					return mockClient
				}(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &DbmyClient{
				accountServiceClient: tt.fields.accountServiceClient,
			}
			got, err := client.GetUserProfile(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserProfile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUserProfile() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getProfileFromHeaders(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name string
		args args
		want *GetUserProfileResponse
	}{
		{
			name: "resolve ProfileType from ProfileID header = RETAIL",
			args: args{
				ctx: func() context.Context {
					ctx := context.Background()
					ctx = commonCtx.WithProfileID(ctx, "MY123456789012")
					return ctx
				}(),
			},
			want: &GetUserProfileResponse{ID: "MY123456789012", Type: ProfileRetail},
		},
		{
			name: "resolve ProfileType from ProfileID header = BUSINESS",
			args: args{
				ctx: func() context.Context {
					ctx := context.Background()
					ctx = commonCtx.WithProfileID(ctx, "BIF1234567")
					return ctx
				}(),
			},
			want: &GetUserProfileResponse{ID: "BIF1234567", Type: ProfileBusiness},
		},
		{
			name: "unknown profileID pattern",
			args: args{
				ctx: func() context.Context {
					ctx := context.Background()
					ctx = commonCtx.WithProfileID(ctx, "ABCD1234")
					return ctx
				}(),
			},
			want: &GetUserProfileResponse{ID: "ABCD1234", Type: ""},
		},
		{
			name: "hardcoded BIF",
			args: args{
				ctx: func() context.Context {
					ctx := context.Background()
					ctx = commonCtx.WithProfileID(ctx, "BIF")
					return ctx
				}(),
			},
			want: nil,
		},
		{
			name: "empty ProfileID header",
			args: args{
				ctx: context.Background(),
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getProfileFromHeaders(tt.args.ctx); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getProfileFromHeaders() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_v2CASAAcctToCstFeature(t *testing.T) {
	type args struct {
		feature *api.Feature
	}
	tests := []struct {
		name string
		args args
		want *Feature
	}{
		{
			name: "empty feature",
			args: args{
				feature: nil,
			},
			want: &Feature{},
		},
		{
			name: "with feature",
			args: args{
				feature: &api.Feature{
					Credit: true,
					Debit:  true,
				},
			},
			want: &Feature{
				Credit: true,
				Debit:  true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := v2CASAAcctToCstFeature(tt.args.feature); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("v2CASAAcctToCstFeature() = %v, want %v", got, tt.want)
			}
		})
	}
}
