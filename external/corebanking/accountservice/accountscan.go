package accountservice

import (
	"context"
	"encoding/json"
	"errors"

	accountserviceAPI "gitlab.com/gx-regional/dbmy/core-banking/account-service/api"
	"gitlab.com/gx-regional/dbmy/partner-integration/common"
)

//nolint:staticcheck
func AccountScan(accountInfo *CustomerAccountDetail, ctx context.Context) error {
	if string(accountInfo.Status) == string(accountserviceAPI.AccountStatus_CLOSED) {
		return errors.New(common.CloseAccount)
	}
	var holdCodes []string
	if applicableHoldCodes, ok := accountInfo.ProductSpecificParameters["applicableHoldcodes"]; ok {
		err := json.Unmarshal([]byte(applicableHoldCodes), &holdCodes)
		if err != nil {
			return errors.New(common.Failextract)
		}
	}
	if AccountIsOnHold(holdCodes) {
		return errors.New(common.OnHoldAccount)
	}

	return nil
}

func AccountIsOnHold(holdcodes []string) bool {
	for _, holdcode := range holdcodes {
		if holdcode == "WHOLE_BALANCE_HOLD" {
			return true
		}
	}
	return false
}
