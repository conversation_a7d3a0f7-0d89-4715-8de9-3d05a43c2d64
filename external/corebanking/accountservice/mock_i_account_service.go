// Code generated by mockery v2.53.3. DO NOT EDIT.

package accountservice

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockIAccountService is an autogenerated mock type for the IAccountService type
type MockIAccountService struct {
	mock.Mock
}

// CheckPermissionsForAccount provides a mock function with given fields: ctx, request
func (_m *MockIAccountService) CheckPermissionsForAccount(ctx context.Context, request *CheckPermissionsForAccountRequest) (*CheckPermissionsForAccountResponse, error) {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for CheckPermissionsForAccount")
	}

	var r0 *CheckPermissionsForAccountResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *CheckPermissionsForAccountRequest) (*CheckPermissionsForAccountResponse, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *CheckPermissionsForAccountRequest) *CheckPermissionsForAccountResponse); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CheckPermissionsForAccountResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *CheckPermissionsForAccountRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAccountDetails provides a mock function with given fields: ctx, request
func (_m *MockIAccountService) GetAccountDetails(ctx context.Context, request *GetAccountDetailsRequest) (*GetAccountDetailsResponse, error) {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for GetAccountDetails")
	}

	var r0 *GetAccountDetailsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetAccountDetailsRequest) (*GetAccountDetailsResponse, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetAccountDetailsRequest) *GetAccountDetailsResponse); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*GetAccountDetailsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetAccountDetailsRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUserProfile provides a mock function with given fields: ctx, request
func (_m *MockIAccountService) GetUserProfile(ctx context.Context, request *GetUserProfileRequest) (*GetUserProfileResponse, error) {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for GetUserProfile")
	}

	var r0 *GetUserProfileResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetUserProfileRequest) (*GetUserProfileResponse, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetUserProfileRequest) *GetUserProfileResponse); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*GetUserProfileResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetUserProfileRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListAccounts provides a mock function with given fields: ctx, request
func (_m *MockIAccountService) ListAccounts(ctx context.Context, request *ListAccountsRequest) (*ListAccountsResponse, error) {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for ListAccounts")
	}

	var r0 *ListAccountsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ListAccountsRequest) (*ListAccountsResponse, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ListAccountsRequest) *ListAccountsResponse); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ListAccountsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ListAccountsRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockIAccountService creates a new instance of MockIAccountService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockIAccountService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockIAccountService {
	mock := &MockIAccountService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
