package accountservice

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"

	accountserviceAPI "gitlab.com/gx-regional/dbmy/core-banking/account-service/api"
	"gitlab.com/gx-regional/dbmy/partner-integration/common"
)

func Test_AccountScan(t *testing.T) {
	type args struct {
		customerAccountDetail *CustomerAccountDetail
		ctx                   context.Context
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "happy path",
			args: args{
				ctx: context.Background(),
				customerAccountDetail: &CustomerAccountDetail{
					Status: AccountStatus(accountserviceAPI.AccountStatus_ACTIVE),
				},
			},
		},
		{
			name: "error path - closed account",
			args: args{
				ctx: context.Background(),
				customerAccountDetail: &CustomerAccountDetail{
					Status: AccountStatus(accountserviceAPI.AccountStatus_CLOSED),
				},
			},
			wantErr: errors.New(common.CloseAccount),
		},
		{
			name: "error path - failed to extract hold code",
			args: args{
				ctx: context.Background(),
				customerAccountDetail: &CustomerAccountDetail{
					ProductSpecificParameters: map[string]string{
						"applicableHoldcodes": "WHOLE_BALANCE_HOLD",
					},
				},
			},
			wantErr: errors.New(common.Failextract),
		},
		{
			name: "error path - on hold code",
			args: args{
				ctx: context.Background(),
				customerAccountDetail: &CustomerAccountDetail{
					ProductSpecificParameters: map[string]string{
						"applicableHoldcodes": func() string {
							got, _ := json.Marshal([]string{"WHOLE_BALANCE_HOLD"})
							return string(got)
						}(),
					},
				},
			},
			wantErr: errors.New(common.OnHoldAccount),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := AccountScan(tt.args.customerAccountDetail, tt.args.ctx); !reflect.DeepEqual(got, tt.wantErr) {
				t.Errorf("AccountScan() = %v, want %v", got, tt.wantErr)
			}
		})
	}
}

func Test_AccountIsOnHold(t *testing.T) {
	type args struct {
		holdCodes []string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "happy path",
			args: args{
				holdCodes: []string{},
			},
			want: false,
		},
		{
			name: "happy path",
			args: args{
				holdCodes: []string{"WHOLE_BALANCE_HOLD"},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := AccountIsOnHold(tt.args.holdCodes); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AccountIsOnHold() = %v, want %v", got, tt.want)
			}
		})
	}
}
