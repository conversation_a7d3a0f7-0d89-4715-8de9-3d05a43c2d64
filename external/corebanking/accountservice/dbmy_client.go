package accountservice

import (
	"context"
	"regexp"
	"strings"

	commonCtx "gitlab.com/gx-regional/dakota/common/context"
	"gitlab.com/gx-regional/dakota/common/tracing"
	api "gitlab.com/gx-regional/dbmy/core-banking/account-service/api"
	client "gitlab.com/gx-regional/dbmy/core-banking/account-service/api/client"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
)

// DbmyClient - defines dbmy account service client
type DbmyClient struct {
	accountServiceClient api.AccountService
}

const (
	msmePrefix = "BIF"
)

var (
	// msmeRegexp -> Prefix has to be BIF and the characters must <= 20
	msmeRegexp = regexp.MustCompile(`^BIF.{0,17}$`)

	// retailRegexp -> Prefix has to be country code by 2 characters and 12 number
	retailRegexp = regexp.MustCompile(`^([A-Za-z]{2})\d{12}$`)
)

// NewDbmyClient instantiates a new Client.
func NewDbmyClient(conf *external.ServiceConfig, tracer tracing.Tracer) (*DbmyClient, error) {
	accountServiceClient, err := client.NewAccountServiceClient(conf.BaseURL, klient.WithServiceName("account-service"), klient.WithTracing(tracer))
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		accountServiceClient: accountServiceClient,
	}, nil
}

// NewDbmyClientWithOptions instantiates a new Client Options.
func NewDbmyClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (*DbmyClient, error) {
	accountServiceClient, err := client.NewAccountServiceClient(conf.BaseURL, options...)
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		accountServiceClient: accountServiceClient,
	}, nil
}

// GetAccountDetails - gets account details for the given account ID
func (c *DbmyClient) GetAccountDetails(ctx context.Context, request *GetAccountDetailsRequest) (*GetAccountDetailsResponse, error) {
	apiResponse, err := c.accountServiceClient.GetAccountDetailsByAccountID(ctx,
		&api.GetAccountRequest{
			AccountID:    request.AccountID,
			FetchBalance: request.FetchBalance,
		})
	if err != nil {
		return nil, err
	}
	response := &GetAccountDetailsResponse{
		Account: &Account{
			Id:                        apiResponse.Account.Id,
			Name:                      apiResponse.Account.Name,
			ParentAccountID:           apiResponse.Account.ParentAccountID,
			CifNumber:                 apiResponse.Account.CifNumber,
			Status:                    AccountStatus(apiResponse.Account.Status),
			ProductID:                 apiResponse.Account.ProductID,
			ProductVariantID:          apiResponse.Account.ProductVariantID,
			ProductSpecificParameters: apiResponse.Account.ProductSpecificParameters,
			OpeningTimestamp:          apiResponse.Account.OpeningTimestamp,
			AccountType:               apiResponse.Account.AccountType,
			PermittedCurrencies:       apiResponse.Account.PermittedCurrencies,
		},
	}
	return response, nil
}

// CheckPermissionsForAccount - checks permission for the given account number
func (c *DbmyClient) CheckPermissionsForAccount(ctx context.Context, request *CheckPermissionsForAccountRequest) (*CheckPermissionsForAccountResponse, error) {
	apiRequest := &api.CheckPermissionsForAccountRequest{
		AccountID: request.AccountID,
		CifNumber: request.CifNumber,
	}

	apiResponse, err := c.accountServiceClient.CheckPermissionsForAccount(ctx, apiRequest)
	if err != nil {
		return nil, err
	}
	return &CheckPermissionsForAccountResponse{Status: AccountPermission(apiResponse.Status)}, nil
}

func (c *DbmyClient) ListAccounts(ctx context.Context, request *ListAccountsRequest) (*ListAccountsResponse, error) {
	apiRequest := &api.ListCASAAccountsForCustomerDetailRequest{
		CifNumber:         request.CifNumber,
		FetchBalance:      true,
		ProductVariantIDs: request.ProductVariantIDs,
	}

	apiResponse, err := c.accountServiceClient.ListCASAAccountsForCustomerDetail(ctx, apiRequest)
	if err != nil {
		return nil, err
	}

	var accounts []CustomerAccountDetail
	for _, account := range apiResponse.Accounts {
		var availableBalance *Money
		if account.AvailableBalance != nil {
			availableBalance = &Money{
				CurrencyCode: account.AvailableBalance.CurrencyCode,
				Val:          account.AvailableBalance.Val,
			}
		}
		accounts = append(accounts, CustomerAccountDetail{
			Id:                        account.Id,
			ParentAccountID:           account.ParentAccountID,
			ProductVariantID:          account.ProductVariantID,
			AccountType:               account.AccountType,
			AccountName:               account.AccountName,
			Status:                    AccountStatus(account.Status),
			OpeningTimestamp:          account.OpeningTimestamp,
			ProductSpecificParameters: account.ProductSpecificParameters,
			AvailableBalance:          availableBalance,
			Features:                  v2CASAAcctToCstFeature(account.Features),
		})
	}

	return &ListAccountsResponse{Accounts: accounts}, nil
}

func v2CASAAcctToCstFeature(features *api.Feature) *Feature {
	if features == nil {
		// return default value to ensure field existence in response
		return &Feature{
			Credit: false,
			Debit:  false,
		}
	}
	return &Feature{
		Credit: features.Credit,
		Debit:  features.Debit,
	}
}

func (c *DbmyClient) GetUserProfile(ctx context.Context, request *GetUserProfileRequest) (*GetUserProfileResponse, error) {
	if profile := getProfileFromHeaders(ctx); profile != nil {
		return profile, nil
	}

	resp, err := c.accountServiceClient.GetAccountDetailsByAccountID(ctx, &api.GetAccountRequest{
		AccountID:    request.AccountID,
		FetchBalance: false,
	})
	if err != nil {
		return nil, err
	}

	profileID := resp.Account.CifNumber
	profileType := ProfileRetail

	if strings.HasPrefix(profileID, msmePrefix) {
		profileType = ProfileBusiness
	}

	return &GetUserProfileResponse{
		ID:   profileID,
		Type: profileType,
	}, nil
}

func getProfileFromHeaders(ctx context.Context) *GetUserProfileResponse {
	// TODO: BIF is hardcoded before BifNumber is properly generated. Remove once this is fixed.
	profileID := commonCtx.GetProfileID(ctx)
	if profileID == "" || profileID == "BIF" {
		return nil
	}

	var profileType ProfileType
	switch {
	case msmeRegexp.MatchString(profileID):
		profileType = ProfileBusiness
	case retailRegexp.MatchString(profileID):
		profileType = ProfileRetail
	}

	return &GetUserProfileResponse{
		ID:   profileID,
		Type: profileType,
	}
}
