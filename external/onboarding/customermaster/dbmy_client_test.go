package customermaster

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"net/http"
	"reflect"
	"testing"

	"gitlab.com/gx-regional/dbmy/customer-master/api/v2"
	mocks "gitlab.com/gx-regional/dbmy/customer-master/api/v2/mock"
	customermasterError "gitlab.com/gx-regional/dbmy/partner-integration/common/customermaster/errors"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
)

func Test_DbmyClient_GetCustomerByHashedIDNumber(t *testing.T) {
	type args struct {
		ctx     context.Context
		request *GetCustomerByHashedIdNumberRequest
	}
	type fields struct {
		customerMasterClient api.CustomerMaster
	}
	tests := []struct {
		name    string
		args    args
		fields  fields
		want    *GetCustomerByHashedIdNumberResponse
		wantErr error
	}{
		{
			name: "happy path",
			args: args{
				ctx:     context.Background(),
				request: &GetCustomerByHashedIdNumberRequest{},
			},
			fields: fields{
				customerMasterClient: func() api.CustomerMaster {
					mockClient := &mocks.CustomerMaster{}
					mockClient.On("GetCustomerByHashedIDNumber", mock.Anything, mock.Anything).Return(&api.GetCustomerByHashedIdNumberResponse{
						CustomerID: "dummy-customer-id",
					}, nil)
					return mockClient
				}(),
			},
			want: &GetCustomerByHashedIdNumberResponse{
				CustomerID: "dummy-customer-id",
			},
		},
		{
			name: "error path",
			args: args{
				ctx:     context.Background(),
				request: &GetCustomerByHashedIdNumberRequest{},
			},
			fields: fields{
				customerMasterClient: func() api.CustomerMaster {
					mockClient := &mocks.CustomerMaster{}
					mockClient.On("GetCustomerByHashedIDNumber", mock.Anything, mock.Anything).Return(nil, errors.New("dummy-error"))
					return mockClient
				}(),
			},
			wantErr: errors.New("dummy-error"),
		},
		{
			name: "error path",
			args: args{
				ctx:     context.Background(),
				request: &GetCustomerByHashedIdNumberRequest{},
			},
			fields: fields{
				customerMasterClient: func() api.CustomerMaster {
					mockClient := &mocks.CustomerMaster{}
					mockClient.On("GetCustomerByHashedIDNumber", mock.Anything, mock.Anything).Return(nil,
						&errorhandling.Error{
							HTTPCode: http.StatusNotFound,
							Code:     api.UserNotFound,
							Message:  "no user found for the given identifier",
						})
					return mockClient
				}(),
			},
			wantErr: customermasterError.ErrUserNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &DbmyClient{
				customerMasterClient: tt.fields.customerMasterClient,
			}
			got, err := client.GetCustomerByHashedIDNumber(tt.args.ctx, tt.args.request)
			if (err != nil) && (err.Error() != tt.wantErr.Error()) {
				t.Errorf("GetCustomerByHashedIDNumber() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCustomerByHashedIDNumber() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_DbmyClient_CreateCustomer(t *testing.T) {
	// sample values for convenience
	var (
		safeID                    = "safeID"
		publicID                  = "cif"
		genderMale                = Gender_Male
		customerTypeTng           = CustomerType_TNG
		customerStatusInactive    = CustomerStatus_Inactive
		purposeOfAccount          = 1
		purposesOfCreatingAccount = &PurposesOfCreatingAccount{
			PurposeOfCreatingAccount_Unknown,
		}
	)
	type args struct {
		ctx     context.Context
		request *CreateCustomerRequest
	}
	type fields struct {
		customerMasterClient api.CustomerMaster
	}
	tests := []struct {
		name    string
		args    args
		fields  fields
		wantErr error
		want    *CreateCustomerResponse
	}{
		{
			name: "minimal input field for happy path",
			args: args{
				ctx: context.Background(),
				request: &CreateCustomerRequest{
					Customer: &Customer{
						Gender: &genderMale,
						Type:   &customerTypeTng,
						Status: &customerStatusInactive,
						ECDDInfo: &ECDDInfo{
							PurposeOfAccount: &purposeOfAccount,
						},
						PurposesOfCreatingAccount: purposesOfCreatingAccount,
					},
				},
			},
			fields: fields{
				customerMasterClient: func() api.CustomerMaster {
					mockClient := &mocks.CustomerMaster{}

					var (
						customer = api.Customer{
							ID:       &safeID,
							PublicID: &publicID,
						}
						customerJSON, _ = json.Marshal(customer)
					)

					mockClient.On("CreateCustomer", mock.Anything, mock.Anything).Return(&api.CreateCustomerResponse{
						Customer: &api.CustomerData{
							Data: customerJSON,
						},
					}, nil)
					return mockClient
				}(),
			},
			want: &CreateCustomerResponse{
				CustomerID: safeID,
				PublicID:   publicID,
			},
		},
		{
			name: "invalid input",
			args: args{
				ctx:     context.Background(),
				request: &CreateCustomerRequest{},
			},
			fields: fields{
				customerMasterClient: func() api.CustomerMaster {
					mockClient := &mocks.CustomerMaster{}
					return mockClient
				}(),
			},
			wantErr: errors.New("unexpected nil Customer"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &DbmyClient{
				customerMasterClient: tt.fields.customerMasterClient,
			}
			got, err := client.CreateCustomer(tt.args.ctx, tt.args.request)
			if (err != nil) && (!assert.Equal(t, err, tt.wantErr)) {
				t.Errorf("CreateCustomer() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateCustomer() = %v, want %v", got, tt.want)
			}
		})
	}
}
