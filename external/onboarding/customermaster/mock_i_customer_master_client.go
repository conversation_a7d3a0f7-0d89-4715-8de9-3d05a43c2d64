// Code generated by mockery v2.53.3. DO NOT EDIT.

package customermaster

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockICustomerMasterClient is an autogenerated mock type for the ICustomerMasterClient type
type MockICustomerMasterClient struct {
	mock.Mock
}

// GetCustomerByHashedIDNumber provides a mock function with given fields: ctx, req
func (_m *MockICustomerMasterClient) GetCustomerByHashedIDNumber(ctx context.Context, req *GetCustomerByHashedIdNumberRequest) (*GetCustomerByHashedIdNumberResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerByHashedIDNumber")
	}

	var r0 *GetCustomerByHashedIdNumberResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetCustomerByHashedIdNumberRequest) (*GetCustomerByHashedIdNumberResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetCustomerByHashedIdNumberRequest) *GetCustomerByHashedIdNumberResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*GetCustomerByHashedIdNumberResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetCustomerByHashedIdNumberRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}


// CreateCustomer provides a mock function with given fields: ctx, req
func (_m *MockICustomerMasterClient) CreateCustomer(ctx context.Context, req *CreateCustomerRequest) (*CreateCustomerResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateCustomer")
	}

	var r0 *CreateCustomerResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *CreateCustomerRequest) (*CreateCustomerResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *CreateCustomerRequest) *CreateCustomerResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CreateCustomerResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *CreateCustomerRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}


// NewMockICustomerMasterClient creates a new instance of MockICustomerMasterClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockICustomerMasterClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockICustomerMasterClient {
	mock := &MockICustomerMasterClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
