package customermaster

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.com/gx-regional/dbmy/partner-integration/common/constant"
	"net/http"

	"gitlab.com/gx-regional/dakota/common/tracing"
	api "gitlab.com/gx-regional/dbmy/customer-master/api/v2"
	"gitlab.com/gx-regional/dbmy/customer-master/api/v2/client"
	customermasterErrors "gitlab.com/gx-regional/dbmy/partner-integration/common/customermaster/errors"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	logTag = "external.customermaster"
)

// DbmyClient - defines dbmy customer master client
type DbmyClient struct {
	customerMasterClient api.CustomerMaster
}

// NewDbmyClient instantiates a new Client.
func NewDbmyClient(conf *external.ServiceConfig, tracer tracing.Tracer) (*DbmyClient, error) {
	customerMasterClient, err := client.NewCustomerMasterClient(conf.BaseURL, klient.WithServiceName("customer-master"), klient.WithTracing(tracer))
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		customerMasterClient: customerMasterClient,
	}, nil
}

// NewDbmyClientWithOptions instantiates a new Client Options.
func NewDbmyClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (*DbmyClient, error) {
	customerMasterClient, err := client.NewCustomerMasterClient(conf.BaseURL, options...)
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		customerMasterClient: customerMasterClient,
	}, nil
}

func (c *DbmyClient) GetCustomerByHashedIDNumber(ctx context.Context, req *GetCustomerByHashedIdNumberRequest) (*GetCustomerByHashedIdNumberResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("IDNumberHashed", req.IdNumberHashed))
	apiResponse, err := c.customerMasterClient.GetCustomerByHashedIDNumber(ctx,
		&api.GetCustomerByHashedIdNumberRequest{
			IdNumberHashed: req.IdNumberHashed,
		})
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "CustomerMasterClient.GetCustomerByHashedIDNumber error", slog.Error(err))
		var servusErr *errorhandling.Error
		ok := errors.As(err, &servusErr)
		if ok && servusErr.HTTPCode == http.StatusNotFound && servusErr.Code == string(customermasterErrors.UserNotFound) {
			return nil, customermasterErrors.ErrUserNotFound
		}
		return nil, err
	}
	response := &GetCustomerByHashedIdNumberResponse{
		CustomerID: apiResponse.CustomerID,
	}
	return response, nil
}

func (c *DbmyClient) CreateCustomer(ctx context.Context, req *CreateCustomerRequest) (*CreateCustomerResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("ID", req.ID))

	createCustomerReq, err := convertCreateCustomerRequest(req)
	if err != nil {
		return nil, err
	}
	res, err := c.customerMasterClient.CreateCustomer(ctx, createCustomerReq)

	if err != nil {
		slog.FromContext(ctx).Error(logTag, "CustomerMasterClient.CreateCustomer error", slog.Error(err))
		return nil, err
	}

	var resCustomer *api.Customer
	err = json.Unmarshal(res.Customer.Data, &resCustomer)
	if err != nil {
		return nil, err
	}

	return &CreateCustomerResponse{
		CustomerID: *resCustomer.ID,
		PublicID:   *resCustomer.PublicID,
	}, nil
}

func convertCreateCustomerRequest(req *CreateCustomerRequest) (*api.CreateCustomerRequest, error) {
	if req == nil {
		return nil, errors.New("unexpected nil CreateCustomerRequest")
	}
	if req.Customer == nil {
		return nil, errors.New("unexpected nil Customer")
	}
	gender, err := toGender(req.Customer.Gender)
	if err != nil {
		return nil, err
	}
	status, err := toCustomerStatus(req.Customer.Status)
	if err != nil {
		return nil, err
	}
	customerType, err := toCustomerType(req.Customer.Type)
	if err != nil {
		return nil, err
	}
	identities, err := toIdentities(req.Customer.Identities)
	if err != nil {
		return nil, err
	}
	contacts, err := toContacts(req.Customer.Contacts)
	if err != nil {
		return nil, err
	}
	addresses, err := toAddresses(req.Customer.Addresses)
	if err != nil {
		return nil, err
	}
	tncAgreements, err := toTnCAgreements(req.Customer.TnCAgreements)
	if err != nil {
		return nil, err
	}
	employments, err := toEmployments(req.Customer.Employments)
	if err != nil {
		return nil, err
	}
	ecddInfo, err := toECDDInfo(req.Customer.ECDDInfo)
	if err != nil {
		return nil, err
	}
	purposesOfCreatingAccount, err := toPurposesOfCreatingAccount(req.Customer.PurposesOfCreatingAccount)
	if err != nil {
		return nil, err
	}

	customer := api.Customer{
		PublicID:                  req.Customer.PublicID,
		Name:                      req.Customer.Name,
		Gender:                    gender,
		DateOfBirth:               req.Customer.DateOfBirth,
		Nationality:               req.Customer.Nationality,
		CountryOfBirth:            req.Customer.CountryOfBirth,
		Status:                    status,
		Type:                      customerType,
		StartDate:                 req.Customer.StartDate,
		Identities:                identities,
		Contacts:                  contacts,
		Addresses:                 addresses,
		TnCAgreements:             tncAgreements,
		Employments:               employments,
		ECDDInfo:                  ecddInfo,
		IsResident:                req.Customer.IsResident,
		NonBankDebtAmount:         req.Customer.NonBankDebtAmount,
		PurposesOfCreatingAccount: purposesOfCreatingAccount,
		Religion:                  req.Customer.Religion,
	}

	customerJSON, err := json.Marshal(customer)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal customer data: %w", err)
	}

	return &api.CreateCustomerRequest{
		ID: req.ID,
		Target: &api.TargetGroup{
			ServiceID: constant.ServiceDigibank,
		},
		Customer: &api.CustomerData{
			Data: customerJSON,
		},
	}, nil
}

func toGender(g *Gender) (*api.Gender, error) {
	if g == nil {
		return nil, errors.New("unexpected gender to be nil")
	}
	genderMap := map[Gender]api.Gender{
		Gender_Male:    api.Gender_Male,
		Gender_Female:  api.Gender_Female,
		Gender_Unknown: api.Gender_Unknown,
	}
	val, ok := genderMap[*g]
	if !ok {
		return nil, fmt.Errorf("invalid gender: %s", *g)
	}
	return &val, nil
}

func toCustomerStatus(s *CustomerStatus) (*api.CustomerStatus, error) {
	if s == nil {
		return nil, errors.New("unexpected customerStatus to be nil")
	}
	customerStatusMap := map[CustomerStatus]api.CustomerStatus{
		CustomerStatus_Unknown:   api.CustomerStatus_Unknown,
		CustomerStatus_Closed:    api.CustomerStatus_Closed,
		CustomerStatus_Inactive:  api.CustomerStatus_Inactive,
		CustomerStatus_Deleted:   api.CustomerStatus_Deleted,
		CustomerStatus_Onboarded: api.CustomerStatus_Onboarded,
	}
	val, ok := customerStatusMap[*s]
	if !ok {
		return nil, fmt.Errorf("invalid customerStatus: %s", *s)
	}
	return &val, nil
}

func toCustomerType(t *CustomerType) (*api.CustomerType, error) {
	if t == nil {
		return nil, errors.New("unexpected customerType to be nil")
	}
	customerTypeMap := map[CustomerType]api.CustomerType{
		CustomerType_TNG:     api.CustomerType_TNG,
		CustomerType_UNKNOWN: api.CustomerType_UNKNOWN,
	}
	val, ok := customerTypeMap[*t]
	if !ok {
		return nil, fmt.Errorf("invalid customerType: %s", *t)
	}
	return &val, nil
}

func toIdentities(i []*Identity) ([]*api.Identity, error) {
	if len(i) == 0 {
		return []*api.Identity{}, nil
	}

	var identities []*api.Identity
	for _, v := range i {
		idType, err := toIDType(v.IDType)
		if err != nil {
			return nil, err
		}

		identities = append(identities, &api.Identity{
			IDType:    idType,
			IDNumber:  v.IDNumber,
			Country:   v.Country,
			IssuedOn:  v.IssuedOn,
			ExpiresOn: v.ExpiresOn,
			IsDeleted: v.IsDeleted,
		})
	}
	return identities, nil
}

func toIDType(t *IdentityType) (*api.IdentityType, error) {
	if t == nil {
		return nil, errors.New("unexpected identityType to be nil")
	}
	identityTypeMap := map[IdentityType]api.IdentityType{
		IdentityType_MYKAD:   api.IdentityType_MYKAD,
		IdentityType_UNKNOWN: api.IdentityType_UNKNOWN,
	}
	val, ok := identityTypeMap[*t]
	if !ok {
		return nil, fmt.Errorf("invalid identityType: %s", *t)
	}
	return &val, nil
}

func toContacts(c []*ContactDetail) ([]*api.ContactDetail, error) {
	if len(c) == 0 {
		return []*api.ContactDetail{}, nil
	}
	var contacts []*api.ContactDetail
	for _, v := range c {
		contactType, err := toContactType(v.ContactType)
		if err != nil {
			return nil, err
		}
		contacts = append(contacts, &api.ContactDetail{
			ContactType:   contactType,
			Email:         v.Email,
			EmailVerified: v.EmailVerified,
			PhoneNumber:   v.PhoneNumber,
		})
	}
	return contacts, nil
}

func toContactType(t *ContactType) (*api.ContactType, error) {
	if t == nil {
		return nil, errors.New("unexpected contactType to be nil")
	}
	contactTypeMap := map[ContactType]api.ContactType{
		ContactType_Primary: api.ContactType_Primary,
	}
	val, ok := contactTypeMap[*t]
	if !ok {
		return nil, fmt.Errorf("invalid contactType: %s", *t)
	}
	return &val, nil
}

func toAddresses(i []*Address) ([]*api.Address, error) {
	if len(i) == 0 {
		return []*api.Address{}, nil
	}

	var addresses []*api.Address
	for _, v := range i {
		addrType, err := toAddressType(v.AddressType)
		if err != nil {
			return nil, err
		}

		addresses = append(addresses, &api.Address{
			AddressType: addrType,
			Street:      v.Street,
			Block:       v.Block,
			Unit:        v.Unit,
			City:        v.City,
			State:       v.State,
			Country:     v.Country,
			PostalCode:  v.PostalCode,
			AddrLine1:   v.AddrLine1,
			AddrLine2:   v.AddrLine2,
		})
	}
	return addresses, nil
}

func toAddressType(t *AddressType) (*api.AddressType, error) {
	if t == nil {
		return nil, errors.New("unexpected AddressType to be nil")
	}

	// Map source types to destination types.
	addressTypeMap := map[AddressType]api.AddressType{
		AddressType_Unknown:    api.AddressType_Unknown,
		AddressType_Registered: api.AddressType_Registered,
		AddressType_Mailing:    api.AddressType_Mailing,
		AddressType_Others:     api.AddressType_Others,
	}

	val, ok := addressTypeMap[*t]
	if !ok {
		return nil, fmt.Errorf("invalid addressType: %s", *t)
	}

	return &val, nil
}

func toAgreementStatus(t *AgreementStatus) (*api.AgreementStatus, error) {
	if t == nil {
		return nil, errors.New("unexpected AgreementStatus to be nil")
	}

	agreementStatusMap := map[AgreementStatus]api.AgreementStatus{
		AgreementStatus_Unaccepted: api.AgreementStatus_Unaccepted,
		AgreementStatus_Accepted:   api.AgreementStatus_Accepted,
	}

	val, ok := agreementStatusMap[*t]
	if !ok {
		return nil, fmt.Errorf("invalid AgreementStatus: %s", *t)
	}

	return &val, nil
}

func toAgreementType(t *AgreementType) (*api.AgreementType, error) {
	if t == nil {
		return nil, errors.New("unexpected AgreementType to be nil")
	}

	agreementTypeMap := map[AgreementType]api.AgreementType{
		AgreementType_Unknown:    api.AgreementType_Unknown,
		AgreementType_Onboarding: api.AgreementType_Onboarding,
		AgreementType_Cards:      api.AgreementType_Cards,
		AgreementType_Lending:    api.AgreementType_Lending,
		AgreementType_Insurance:  api.AgreementType_Insurance,
		AgreementType_Chatbot:    api.AgreementType_Chatbot,
	}

	val, ok := agreementTypeMap[*t]
	if !ok {
		return nil, fmt.Errorf("invalid AgreementType: %s", *t)
	}

	return &val, nil
}

func toTnCAgreements(i []*TnCAgreement) ([]*api.TnCAgreement, error) {
	if len(i) == 0 {
		return []*api.TnCAgreement{}, nil
	}

	var agreements []*api.TnCAgreement
	for _, v := range i {
		status, err := toAgreementStatus(v.Status)
		if err != nil {
			return nil, err
		}

		agreementType, err := toAgreementType(v.AgreementType)
		if err != nil {
			return nil, err
		}

		agreements = append(agreements, &api.TnCAgreement{
			AgreementID:   v.AgreementID,
			AgreementType: agreementType,
			Status:        status,
		})
	}
	return agreements, nil
}

func toEmploymentType(t *EmploymentType) (*api.EmploymentType, error) {
	if t == nil {
		return nil, errors.New("unexpected EmploymentType to be nil")
	}

	employmentTypeMap := map[EmploymentType]api.EmploymentType{
		EmploymentType_Unknown:         api.EmploymentType_Unknown,
		EmploymentType_Current:         api.EmploymentType_Current,
		EmploymentType_SelfEmployed:    api.EmploymentType_SelfEmployed,
		EmploymentType_PublicEmployee:  api.EmploymentType_PublicEmployee,
		EmploymentType_PrivateEmployee: api.EmploymentType_PrivateEmployee,
		EmploymentType_Retired:         api.EmploymentType_Retired,
		EmploymentType_Student:         api.EmploymentType_Student,
		EmploymentType_Unemployed:      api.EmploymentType_Unemployed,
	}

	val, ok := employmentTypeMap[*t]
	if !ok {
		return nil, fmt.Errorf("invalid EmploymentType: %s", *t)
	}

	return &val, nil
}

func toEmployments(i []*Employment) ([]*api.Employment, error) {
	if len(i) == 0 {
		return []*api.Employment{}, nil
	}

	var employments []*api.Employment
	for _, v := range i {
		empType, err := toEmploymentType(v.Type)
		if err != nil {
			return nil, err
		}

		employments = append(employments, &api.Employment{
			Type:              empType,
			AnnualIncome:      v.AnnualIncome,
			SSICCode:          v.SSICCode,
			PrimarySSICCode:   v.PrimarySSICCode,
			SecondarySSICCode: v.SecondarySSICCode,
			Occupation:        v.Occupation,
			EmployerName:      v.EmployerName,
			SourceOfFunds:     v.SourceOfFunds,
			NatureOfBusiness:  v.NatureOfBusiness,
			MonthlyIncome:     v.MonthlyIncome,
		})
	}
	return employments, nil
}

func toSourceOfFund(t *SourceOfFund) (*api.SourceOfFund, error) {
	if t == nil {
		return nil, errors.New("unexpected SourceOfFund to be nil")
	}

	sourceOfFundMap := map[SourceOfFund]api.SourceOfFund{
		SourceOfFund_Unknown:                          api.SourceOfFund_Unknown,
		SourceOfFund_ReturnOnInvestments:              api.SourceOfFund_ReturnOnInvestments,
		SourceOfFund_SavingsFromEmploymentIncome:      api.SourceOfFund_SavingsFromEmploymentIncome,
		SourceOfFund_SavingsFromBusinessIncome:        api.SourceOfFund_SavingsFromBusinessIncome,
		SourceOfFund_DividendsFromHoldingListCoShares: api.SourceOfFund_DividendsFromHoldingListCoShares,
		SourceOfFund_SaleOfProperty:                   api.SourceOfFund_SaleOfProperty,
		SourceOfFund_SaleOfInvestmentProduct:          api.SourceOfFund_SaleOfInvestmentProduct,
		SourceOfFund_ParentSavings:                    api.SourceOfFund_ParentSavings,
	}

	val, ok := sourceOfFundMap[*t]
	if !ok {
		return nil, fmt.Errorf("invalid SourceOfFund: %s", *t)
	}

	return &val, nil
}

func toSourceOfWealths(i []*SourceOfWealth) []*api.SourceOfWealth {
	if len(i) == 0 {
		return []*api.SourceOfWealth{}
	}

	var sow []*api.SourceOfWealth
	for _, v := range i {
		sow = append(sow, &api.SourceOfWealth{
			SOWType:    v.SOWType,
			SOWDetails: v.SOWDetails,
		})
	}
	return sow
}

func toSourceOfFunds(i []*SourceOfFunds) ([]*api.SourceOfFunds, error) {
	if len(i) == 0 {
		return []*api.SourceOfFunds{}, nil
	}

	var sof []*api.SourceOfFunds
	for _, v := range i {
		sourceOfFund, err := toSourceOfFund(v.SourceOfFund)
		if err != nil {
			return nil, err
		}

		sof = append(sof, &api.SourceOfFunds{
			SourceOfFund:            sourceOfFund,
			CountryOfOriginsOfFunds: v.CountryOfOriginsOfFunds,
			AmountToBeTransferred:   v.AmountToBeTransferred,
			FirstPartyTransfer:      v.FirstPartyTransfer,
		})
	}
	return sof, nil
}

func toECDDInfo(e *ECDDInfo) (*api.ECDDInfo, error) {
	if e == nil {
		return nil, errors.New("unexpected ECDDInfo to be nil")
	}

	sourceOfWealths := toSourceOfWealths(e.SourceOfWealth)

	sourceOfFunds, err := toSourceOfFunds(e.SourceOfFunds)
	if err != nil {
		return nil, err
	}

	return &api.ECDDInfo{
		PurposeOfAccount:             e.PurposeOfAccount,
		PurposeOfCreatingAccount:     e.PurposeOfCreatingAccount,
		SourceOfWealth:               sourceOfWealths,
		SourceOfFunds:                sourceOfFunds,
		AnticipatedAmount:            e.AnticipatedAmount,
		AnticipatedNumOfTransactions: e.AnticipatedNumOfTransactions,
		MonthlyTxnAmount:             e.MonthlyTxnAmount,
		AnnualSales:                  e.AnnualSales,
		EmployeeCount:                e.EmployeeCount,
		LoanPurpose:                  e.LoanPurpose,
	}, nil
}

func toPurposesOfCreatingAccount(p *PurposesOfCreatingAccount) (*api.PurposesOfCreatingAccount, error) {
	if p == nil {
		return nil, errors.New("unexpected PurposesOfCreatingAccount to be nil")
	}
	if len(*p) == 0 {
		return &api.PurposesOfCreatingAccount{}, nil
	}
	purposeOfCreatingAccountMap := map[PurposeOfCreatingAccount]api.PurposeOfCreatingAccount{
		PurposeOfCreatingAccount_Unknown:              api.PurposeOfCreatingAccount_Unknown,
		PurposeOfCreatingAccount_Savings:              api.PurposeOfCreatingAccount_Savings,
		PurposeOfCreatingAccount_EverydayTransactions: api.PurposeOfCreatingAccount_EverydayTransactions,
		PurposeOfCreatingAccount_FinancialPlanning:    api.PurposeOfCreatingAccount_FinancialPlanning,
	}

	var purposeOfCreatingAccounts api.PurposesOfCreatingAccount
	for _, v := range *p {
		val, ok := purposeOfCreatingAccountMap[v]
		if !ok {
			return nil, fmt.Errorf("invalid purposeOfCreatingAccount: %s", v)
		}
		purposeOfCreatingAccounts = append(purposeOfCreatingAccounts, val)
	}

	return &purposeOfCreatingAccounts, nil
}
