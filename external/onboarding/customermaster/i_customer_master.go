// Package customermaster provide the wrapper to return customer master service client based on service group name from config .
package customermaster

import (
	"context"
	"errors"

	"gitlab.com/gx-regional/dbmy/partner-integration/common"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
)

//go:generate mockery --name=ICustomerMasterClient --inpackage --case=underscore
type ICustomerMasterClient interface {
	GetCustomerByHashedIDNumber(ctx context.Context, req *GetCustomerByHashedIdNumberRequest) (*GetCustomerByHashedIdNumberResponse, error)
	CreateCustomer(ctx context.Context, req *CreateCustomerRequest) (*CreateCustomerResponse, error)
}

// NewClientWithOptions instantiates implementation of ICustomerMasterClient based on service group name
func NewClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (ICustomerMasterClient, error) {
	if conf.GroupName == common.GroupNameDBMY {
		return NewDbmyClientWithOptions(conf, options...)
	}
	return nil, errors.New("invalid value for customermaster.groupName")
}
