package customermaster

import "time"

type GetCustomerByHashedIdNumberRequest struct {
	IdNumberHashed string `json:"idNumberHashed,omitempty"`
}

type GetCustomerByHashedIdNumberResponse struct {
	CustomerID string `json:"customerID,omitempty"`
}

type CreateCustomerRequest struct {
	ID       string    `json:"ID,omitempty"`
	Customer *Customer `json:"customer,omitempty"`
}

type CreateCustomerResponse struct {
	CustomerID string `json:"customerID,omitempty"`
	PublicID   string `json:"publicID,omitempty"`
}

type Gender string

const (
	Gender_Male    Gender = "MALE"
	Gender_Female  Gender = "FEMALE"
	Gender_Unknown Gender = "UNKNOWN"
)

type CustomerStatus string

const (
	CustomerStatus_Unknown   CustomerStatus = "UNKNOWN"
	CustomerStatus_Inactive  CustomerStatus = "INACTIVE"
	CustomerStatus_Onboarded CustomerStatus = "ONBOARDED"
	CustomerStatus_Closed    CustomerStatus = "CLOSED"
	CustomerStatus_Deleted   CustomerStatus = "DELETED"
)

type CustomerType string

const (
	CustomerType_UNKNOWN CustomerType = "UNKNOWN"
	CustomerType_TNG     CustomerType = "TNG"
)

type Customer struct {
	ID                        *string                    `json:"ID,omitempty"`
	PublicID                  *string                    `json:"publicID,omitempty"`
	Name                      *string                    `json:"name,omitempty"`
	PreferredName             *string                    `json:"preferredName,omitempty"`
	HanyuPinyinName           *string                    `json:"hanyuPinyinName,omitempty"`
	Alias                     *string                    `json:"alias,omitempty"`
	Gender                    *Gender                    `json:"gender,omitempty"`
	DateOfBirth               *string                    `json:"dateOfBirth,omitempty"`
	Nationality               *string                    `json:"nationality,omitempty"`
	CountryOfBirth            *string                    `json:"countryOfBirth,omitempty"`
	Status                    *CustomerStatus            `json:"status,omitempty"`
	Type                      *CustomerType              `json:"type,omitempty"`
	StartDate                 *string                    `json:"startDate,omitempty"`
	RelatedCounterpartyInd    *bool                      `json:"relatedCounterPartyInd,omitempty"`
	Identities                []*Identity                `json:"identities,omitempty"`
	Contacts                  []*ContactDetail           `json:"contacts,omitempty"`
	Addresses                 []*Address                 `json:"addresses,omitempty"`
	TnCAgreements             []*TnCAgreement            `json:"tncAgreements,omitempty"`
	Employments               []*Employment              `json:"employments,omitempty"`
	ECDDInfo                  *ECDDInfo                  `json:"ecddInfo,omitempty"`
	CreatedAt                 time.Time                  `json:"createdAt,omitempty"`
	IsResident                *bool                      `json:"isResident,omitempty"`
	Religion                  *string                    `json:"religion,omitempty"`
	HashedIdentity            *string                    `json:"hashedIdentity,omitempty"`
	NonBankDebtAmount         *uint                      `json:"nonBankDebtAmount,omitempty"`
	PurposesOfCreatingAccount *PurposesOfCreatingAccount `json:"purposesOfCreatingAccount,omitempty"`
}
type IdentityType string

const (
	IdentityType_UNKNOWN IdentityType = "UNKNOWN"
	IdentityType_MYKAD   IdentityType = "MYKAD"
)

type Identity struct {
	IDType    *IdentityType `json:"IDType,omitempty"`
	IDNumber  *string       `json:"IDNumber,omitempty"`
	Country   *string       `json:"country,omitempty"`
	IssuedOn  *string       `json:"issuedOn,omitempty"`
	ExpiresOn *string       `json:"expiresOn,omitempty"`
	IsDeleted *bool         `json:"isDeleted,omitempty"`
}

type ContactDetail struct {
	ContactType   *ContactType `json:"contactType,omitempty"`
	Email         *string      `json:"email,omitempty"`
	EmailVerified *bool        `json:"emailVerified,omitempty"`
	PhoneNumber   *string      `json:"phoneNumber,omitempty"`
}

type ContactType string

const (
	ContactType_Primary ContactType = "PRIMARY"
)

type AddressType string

const (
	AddressType_Unknown    AddressType = "UNKNOWN"
	AddressType_Registered AddressType = "REGISTERED"
	AddressType_Mailing    AddressType = "MAILING"
	AddressType_Others     AddressType = "OTHERS"
)

// Country is ISO 3166-1 country code
type Country string

type Address struct {
	AddressType *AddressType `json:"addressType,omitempty"`
	Street      *string      `json:"street,omitempty"`
	Block       *string      `json:"block,omitempty"`
	Unit        *string      `json:"unit,omitempty"`
	City        *string      `json:"city,omitempty"`
	State       *string      `json:"state,omitempty"`
	Country     *string      `json:"country,omitempty"`
	PostalCode  *string      `json:"postalCode,omitempty"`
	AddrLine1   *string      `json:"addrLine1,omitempty"`
	AddrLine2   *string      `json:"addrLine2,omitempty"`
}

type AgreementStatus string

const (
	AgreementStatus_Unaccepted AgreementStatus = "UNACCEPTED"
	AgreementStatus_Accepted   AgreementStatus = "ACCEPTED"
)

type AgreementType string

const (
	AgreementType_Unknown    AgreementType = "UNKNOWN"
	AgreementType_Onboarding AgreementType = "ONBOARDING"
	AgreementType_Cards      AgreementType = "CARDS"
	AgreementType_Lending    AgreementType = "LENDING"
	AgreementType_Insurance  AgreementType = "INSURANCE"
	AgreementType_Chatbot    AgreementType = "CHATBOT"
)

type TnCAgreement struct {
	AgreementID   *string          `json:"agreementID,omitempty"`
	AgreementType *AgreementType   `json:"agreementType,omitempty"`
	Status        *AgreementStatus `json:"status,omitempty"`
}

type EmploymentType string

const (
	EmploymentType_Unknown         EmploymentType = "UNKNOWN"
	EmploymentType_Current         EmploymentType = "CURRENT"
	EmploymentType_SelfEmployed    EmploymentType = "SELF_EMPLOYED"
	EmploymentType_PublicEmployee  EmploymentType = "PUBLIC_EMPLOYEE"
	EmploymentType_PrivateEmployee EmploymentType = "PRIVATE_EMPLOYEE"
	EmploymentType_Retired         EmploymentType = "RETIRED"
	EmploymentType_Student         EmploymentType = "STUDENT"
	EmploymentType_Unemployed      EmploymentType = "UNEMPLOYED"
)

type Employment struct {
	Type              *EmploymentType `json:"type,omitempty"`
	AnnualIncome      *string         `json:"annualIncome,omitempty"`
	SSICCode          *string         `json:"ssicCode,omitempty"`
	PrimarySSICCode   *string         `json:"primarySSICCode,omitempty"`
	SecondarySSICCode *string         `json:"secondarySSICCode,omitempty"`
	Occupation        *string         `json:"occupation,omitempty"`
	EmployerName      *string         `json:"employerName,omitempty"`
	SourceOfFunds     *string         `json:"sourceOfFunds,omitempty"`
	NatureOfBusiness  *string         `json:"natureOfBusiness,omitempty"`
	MonthlyIncome     *string         `json:"monthlyIncome,omitempty"`
}

type ECDDInfo struct {
	PurposeOfAccount             *int              `json:"purposeOfAccount,omitempty"`
	PurposeOfCreatingAccount     *[]int            `json:"purposeOfCreatingAccount,omitempty"`
	SourceOfWealth               []*SourceOfWealth `json:"sourceOfWealth,omitempty"`
	SourceOfFunds                []*SourceOfFunds  `json:"sourceOfFunds,omitempty"`
	AnticipatedAmount            *int              `json:"anticipatedAmount,omitempty"`
	AnticipatedNumOfTransactions *int              `json:"anticipatedNumOfTransactions,omitempty"`
	MonthlyTxnAmount             *int              `json:"monthlyTxnAmount,omitempty"`
	AnnualSales                  *int              `json:"annualSales,omitempty"`
	EmployeeCount                *int              `json:"employeeCount,omitempty"`
	LoanPurpose                  *string           `json:"loanPurpose,omitempty"`
}

type SourceOfWealth struct {
	SOWType    *int    `json:"type,omitempty"`
	SOWDetails *string `json:"details,omitempty"`
}

type SourceOfFunds struct {
	SourceOfFund            *SourceOfFund `json:"sourceOfFund,omitempty"`
	CountryOfOriginsOfFunds *string       `json:"countryOfOriginsOfFunds,omitempty"`
	AmountToBeTransferred   *float64      `json:"amountToBeTransferred,omitempty"`
	FirstPartyTransfer      *string       `json:"firstPartyTransfer,omitempty"`
}

type SourceOfFund string

const (
	SourceOfFund_Unknown                          SourceOfFund = "SourceOfFund_Unknown"
	SourceOfFund_ReturnOnInvestments              SourceOfFund = "SourceOfFund_ReturnOnInvestments"
	SourceOfFund_SavingsFromEmploymentIncome      SourceOfFund = "SourceOfFund_SavingsFromEmploymentIncome"
	SourceOfFund_SavingsFromBusinessIncome        SourceOfFund = "SourceOfFund_SavingsFromBusinessIncome"
	SourceOfFund_DividendsFromHoldingListCoShares SourceOfFund = "SourceOfFund_DividendsFromHoldingListCoShares"
	SourceOfFund_SaleOfProperty                   SourceOfFund = "SourceOfFund_SaleOfProperty"
	SourceOfFund_SaleOfInvestmentProduct          SourceOfFund = "SourceOfFund_SaleOfInvestmentProduct"
	SourceOfFund_ParentSavings                    SourceOfFund = "SourceOfFund_ParentSavings"
)

type PurposesOfCreatingAccount []PurposeOfCreatingAccount
type PurposeOfCreatingAccount string

const (
	PurposeOfCreatingAccount_Unknown              PurposeOfCreatingAccount = "UNKNOWN"
	PurposeOfCreatingAccount_Savings              PurposeOfCreatingAccount = "SAVINGS"
	PurposeOfCreatingAccount_EverydayTransactions PurposeOfCreatingAccount = "EVERYDAY_TRANSACTIONS"
	PurposeOfCreatingAccount_FinancialPlanning    PurposeOfCreatingAccount = "FINANCIAL_PLANNING"
)
