package customerexperience

import (
	"context"
	"fmt"
	"gitlab.com/gx-regional/dakota/common/tracing"
	api "gitlab.com/gx-regional/dbmy/customer-experience/api"
	"gitlab.com/gx-regional/dbmy/customer-experience/api/client"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	logTag = "external.customerexperience"
)

// DbmyClient - defines dbmy customer experience client
type DbmyClient struct {
	customerExperienceClient api.CustomerExperience
}

// NewDbmyClient instantiates a new Client.
func NewDbmyClient(conf *external.ServiceConfig, tracer tracing.Tracer) (*DbmyClient, error) {
	customerExperienceClient, err := client.NewCustomerExperienceClient(conf.BaseURL, klient.WithServiceName("customer-experience"), klient.WithTracing(tracer))
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		customerExperienceClient: customerExperienceClient,
	}, nil
}

// NewDbmyClientWithOptions instantiates a new Client Options.
func NewDbmyClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (*DbmyClient, error) {
	customerExperienceClient, err := client.NewCustomerExperienceClient(conf.BaseURL, options...)
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		customerExperienceClient: customerExperienceClient,
	}, nil
}

func (c *DbmyClient) CreateEcosystemID(ctx context.Context, req *CreateEcosystemIDRequest) (*CreateEcosystemIDResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("SafeID", req.SafeID))

	var source api.CreateEcosystemIDRequest_Source
	switch req.Source {
	case Source_Digibank:
		source = api.CreateEcosystemIDRequest_Source_Digibank
	default:
		return nil, fmt.Errorf("unsupported source: %v", req.Source)
	}

	res, err := c.customerExperienceClient.CreateEcosystemID(ctx, &api.CreateEcosystemIDRequest{
		SafeID: req.SafeID,
		Source: source,
	})

	if err != nil {
		slog.FromContext(ctx).Error(logTag, "CustomerExperienceClient.CreateEcosystemID error", slog.Error(err))
		return nil, err
	}

	return &CreateEcosystemIDResponse{
		EcosystemID: res.EcosystemID,
	}, nil
}
