package customerexperience

import (
	"context"
	"errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"testing"

	customerExperienceApi "gitlab.com/gx-regional/dbmy/customer-experience/api"
	mocks "gitlab.com/gx-regional/dbmy/customer-experience/api/mock"
)

func Test_DbmyClient_CreateEcosystemID(t *testing.T) {
	var (
		mockCustomerExperienceClient = &mocks.CustomerExperience{}
		client                       = &DbmyClient{
			customerExperienceClient: mockCustomerExperienceClient,
		}
	)
	t.Run("happy path", func(t *testing.T) {
		var (
			input = &CreateEcosystemIDRequest{
				SafeID: "SafeID",
				Source: Source_Digibank,
			}
			expected = &CreateEcosystemIDResponse{
				EcosystemID: "EcosystemID",
			}
		)

		mockCustomerExperienceClient.On("CreateEcosystemID", mock.Anything, mock.Anything).
			Return(&customerExperienceApi.CreateEcosystemIDResponse{
				EcosystemID: "EcosystemID",
			}, nil).Once()

		got, err := client.CreateEcosystemID(context.Background(), input)

		assert.Nil(t, err)
		assert.Equal(t, expected, got)
	})
	t.Run("sad path", func(t *testing.T) {
		t.Run("invalid source provided", func(t *testing.T) {
			var (
				input = &CreateEcosystemIDRequest{
					SafeID: "SafeID",
					Source: Source("random source"),
				}
				expected = errors.New("unsupported source: random source")
			)

			got, err := client.CreateEcosystemID(context.Background(), input)

			assert.Nil(t, got)
			assert.Equal(t, expected, err)
		})
		t.Run("error from customer-experience", func(t *testing.T) {
			var (
				input = &CreateEcosystemIDRequest{
					SafeID: "SafeID",
					Source: Source_Digibank,
				}
				expected = errors.New("random err")
			)

			mockCustomerExperienceClient.On("CreateEcosystemID", mock.Anything, mock.Anything).
				Return(nil, errors.New("random err")).Once()

			got, err := client.CreateEcosystemID(context.Background(), input)

			assert.Nil(t, got)
			assert.Equal(t, expected, err)
		})
	})
}
