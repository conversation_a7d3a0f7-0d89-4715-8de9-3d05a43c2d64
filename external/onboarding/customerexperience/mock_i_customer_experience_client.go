// Code generated by mockery v2.53.3. DO NOT EDIT.

package customerexperience

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockICustomerExperienceClient is an autogenerated mock type for the ICustomerExperienceClient type
type MockICustomerExperienceClient struct {
	mock.Mock
}

// CreateEcosystemID provides a mock function with given fields: ctx, req
func (_m *MockICustomerExperienceClient) CreateEcosystemID(ctx context.Context, req *CreateEcosystemIDRequest) (*CreateEcosystemIDResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateEcosystemID")
	}

	var r0 *CreateEcosystemIDResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *CreateEcosystemIDRequest) (*CreateEcosystemIDResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *CreateEcosystemIDRequest) *CreateEcosystemIDResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CreateEcosystemIDResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *CreateEcosystemIDRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}


// NewMockICustomerExperienceClient creates a new instance of MockICustomerExperienceClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockICustomerExperienceClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockICustomerExperienceClient {
	mock := &MockICustomerExperienceClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
