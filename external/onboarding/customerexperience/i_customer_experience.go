// Package customerexperience provide the wrapper to return customer master service client based on service group name from config .
package customerexperience

import (
	"context"
	"errors"

	"gitlab.com/gx-regional/dbmy/partner-integration/common"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
)

//go:generate mockery --name=ICustomerExperienceClient --inpackage --case=underscore
type ICustomerExperienceClient interface {
	CreateEcosystemID(ctx context.Context, req *CreateEcosystemIDRequest) (*CreateEcosystemIDResponse, error)
}

// NewClientWithOptions instantiates implementation of ICustomerExperienceClient based on service group name
func NewClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (ICustomerExperienceClient, error) {
	if conf.GroupName == common.GroupNameDBMY {
		return NewDbmyClientWithOptions(conf, options...)
	}
	return nil, errors.New("invalid value for customerexperience.groupName")
}
