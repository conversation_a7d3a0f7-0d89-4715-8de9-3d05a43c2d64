package applicationservice

/**
all DTOs in this package is manually create to attempt to abstract out the various potentially different
DTO for each region.

It's done by referencing application-service's original DTO, but some extra unused fields are removed for simplicity's sake.
Apply change accordingly when any abstraction breaks
*/
import (
	"encoding/json"
)

type CreateApplicationRequest struct {
	ReferenceID     string             `json:"referenceID,omitempty"`
	CustomerID      string             `json:"customerID,omitempty"`
	ApplicationType ApplicationType    `json:"applicationType,omitempty"`
	Channel         ApplicationChannel `json:"channel,omitempty"`
	Fields          []FieldRequest     `json:"fields,omitempty"`
	Template        string             `json:"template,omitempty"`
}

type CreateApplicationResponse struct {
	ApplicationID string `json:"applicationID,omitempty"`
}

type FieldRequest struct {
	Key      string          `json:"key,omitempty"`
	Value    string          `json:"value,omitempty"`
	Metadata json.RawMessage `json:"metadata,omitempty"`
}

type ApplicationType string

const (
	ApplicationType_AT_UNDEFINED   ApplicationType = "AT_UNDEFINED"
	ApplicationType_AT_PARTNER_LOC ApplicationType = "AT_PARTNER_LOC"
)

type ApplicationChannel string

const (
	ApplicationChannel_UNDEFINED   ApplicationChannel = "UNDEFINED"
	ApplicationChannel_PARTNER_LOC ApplicationChannel = "PARTNER_LOC"
)
