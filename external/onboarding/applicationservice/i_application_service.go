// Package applicationservice provide the wrapper to return application service client based on service group name from config .
package applicationservice

import (
	"context"
	"errors"

	"gitlab.com/gx-regional/dbmy/partner-integration/common"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
)

//go:generate mockery --name=IApplicationServiceClient --inpackage --case=underscore
type IApplicationServiceClient interface {
	CreateApplication(ctx context.Context, req *CreateApplicationRequest) (*CreateApplicationResponse, error)
}

// NewClientWithOptions instantiates implementation of IApplicationServiceClient based on service group name
func NewClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (IApplicationServiceClient, error) {
	if conf.GroupName == common.GroupNameDBMY {
		return NewDbmyClientWithOptions(conf, options...)
	}
	return nil, errors.New("invalid value for applicationservice.groupName")
}
