// Code generated by mockery v2.53.3. DO NOT EDIT.

package applicationservice

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockIApplicationServiceClient is an autogenerated mock type for the IApplicationServiceClient type
type MockIApplicationServiceClient struct {
	mock.Mock
}

// CreateApplication provides a mock function with given fields: ctx, req
func (_m *MockIApplicationServiceClient) CreateApplication(ctx context.Context, req *CreateApplicationRequest) (*CreateApplicationResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateApplication")
	}

	var r0 *CreateApplicationResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *CreateApplicationRequest) (*CreateApplicationResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *CreateApplicationRequest) *CreateApplicationResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CreateApplicationResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *CreateApplicationRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockIApplicationServiceClient creates a new instance of MockIApplicationServiceClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockIApplicationServiceClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockIApplicationServiceClient {
	mock := &MockIApplicationServiceClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
