package applicationservice

import (
	"context"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dakota/servus/v2"
	"testing"

	applicationServiceApi "gitlab.com/gx-regional/dbmy/application-service/api"
)

func Test_DbmyClient_CreateApplication(t *testing.T) {
	// shared convenience
	var (
		mockApplicationServiceClient = &applicationServiceApi.MockApplicationService{}
		client                       = &DbmyClient{
			applicationServiceClient: mockApplicationServiceClient,
		}
	)
	t.Run("happy path", func(t *testing.T) {
		var (
			req = &CreateApplicationRequest{
				ApplicationType: ApplicationType_AT_PARTNER_LOC,
				Channel:         ApplicationChannel_PARTNER_LOC,
			}
			want = &CreateApplicationResponse{
				ApplicationID: "uuid",
			}
		)

		mockApplicationServiceClient.On("CreateApplication", mock.Anything, mock.Anything).Return(&applicationServiceApi.CreateApplicationResponse{
			Application: &applicationServiceApi.ApplicationResponse{
				UUID: "uuid",
			},
		}, nil).Once()
		got, err := client.CreateApplication(context.Background(), req)

		assert.NoError(t, err)
		assert.Equal(t, want, got)
	})

	t.Run("error path", func(t *testing.T) {
		var (
			req = &CreateApplicationRequest{
				ApplicationType: ApplicationType_AT_PARTNER_LOC,
				Channel:         ApplicationChannel_PARTNER_LOC,
			}
			want = servus.InternalServerError(applicationServiceApi.DatabaseError, "something went wrong")
		)

		mockApplicationServiceClient.On("CreateApplication", mock.Anything, mock.Anything).
			Return(nil, servus.InternalServerError(applicationServiceApi.DatabaseError, "something went wrong")).Once()
		_, err := client.CreateApplication(context.Background(), req)

		assert.EqualError(t, want, err.Error())
	})
}
