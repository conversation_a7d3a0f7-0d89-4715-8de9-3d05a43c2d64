package applicationservice

import (
	"context"
	"fmt"
	"gitlab.com/gx-regional/dakota/common/tracing"
	applicationServiceApi "gitlab.com/gx-regional/dbmy/application-service/api"
	"gitlab.com/gx-regional/dbmy/application-service/api/client"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	logTag = "external.applicationservice"
)

// DbmyClient - defines dbmy application service client
type DbmyClient struct {
	applicationServiceClient applicationServiceApi.ApplicationService
}

// NewDbmyClient instantiates a new Client.
func NewDbmyClient(conf *external.ServiceConfig, tracer tracing.Tracer) (*DbmyClient, error) {
	applicationServiceClient, err := client.NewApplicationServiceClient(conf.BaseURL, klient.WithServiceName("application-service"), klient.WithTracing(tracer))
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		applicationServiceClient: applicationServiceClient,
	}, nil
}

// NewDbmyClientWithOptions instantiates a new Client Options.
func NewDbmyClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (*DbmyClient, error) {
	applicationServiceClient, err := client.NewApplicationServiceClient(conf.BaseURL, options...)
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		applicationServiceClient: applicationServiceClient,
	}, nil
}

func (c *DbmyClient) CreateApplication(ctx context.Context, req *CreateApplicationRequest) (*CreateApplicationResponse, error) {
	ctx = slog.AddTagsToContext(ctx,
		slog.CustomTag("ReferenceID", req.ReferenceID),
		slog.CustomTag("CustomerID", req.CustomerID),
	)

	convertedReq, convertErr := convertCreateApplicationRequest(req)
	if convertErr != nil {
		return nil, convertErr
	}

	apiResponse, err := c.applicationServiceClient.CreateApplication(ctx, convertedReq)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "ApplicationServiceClient.CreateApplication error", slog.Error(err))
		return nil, err
	}
	response := &CreateApplicationResponse{
		ApplicationID: apiResponse.Application.UUID,
	}
	return response, nil
}

func convertCreateApplicationRequest(req *CreateApplicationRequest) (*applicationServiceApi.CreateApplicationRequest, error) {
	applicationType, applicationTypeErr := convertApplicationType(req.ApplicationType)
	if applicationTypeErr != nil {
		return nil, applicationTypeErr
	}

	applicationChannel, applicationChannelErr := convertApplicationChannel(req.Channel)
	if applicationChannelErr != nil {
		return nil, applicationChannelErr
	}

	return &applicationServiceApi.CreateApplicationRequest{
		ReferenceID:     req.ReferenceID,
		CustomerID:      req.CustomerID,
		ApplicationType: applicationType,
		Channel:         applicationChannel,
		Fields:          convertFieldRequests(req.Fields),
		Template:        req.Template,
	}, nil
}

func convertApplicationType(applicationType ApplicationType) (applicationServiceApi.ApplicationType, error) {
	switch applicationType {
	case ApplicationType_AT_PARTNER_LOC:
		return applicationServiceApi.ApplicationType_AT_PARTNER_LOC, nil
	default:
		return applicationServiceApi.ApplicationType_AT_UNDEFINED, fmt.Errorf("unsupported application type: %s", applicationType)
	}
}

func convertApplicationChannel(applicationChannel ApplicationChannel) (applicationServiceApi.ApplicationChannel, error) {
	switch applicationChannel {
	case ApplicationChannel_PARTNER_LOC:
		return applicationServiceApi.ApplicationChannel_PARTNER_LOC, nil
	default:
		return applicationServiceApi.ApplicationChannel_UNDEFINED, fmt.Errorf("unsupported application channel: %s", applicationChannel)
	}
}

func convertFieldRequests(req []FieldRequest) []applicationServiceApi.FieldRequest {
	var result []applicationServiceApi.FieldRequest
	for _, field := range req {
		result = append(result, applicationServiceApi.FieldRequest{
			Key:      field.Key,
			Value:    field.Value,
			Metadata: field.Metadata,
		})
	}

	return result
}
