package klientopts

import (
	"errors"
	"reflect"
	"testing"

	servus "gitlab.myteksi.net/dakota/servus/v2"
)

func Test_createUserErrorHandler(t *testing.T) {
	// A slice of integers representing HTTP status codes that should be ignored as "non-threats".
	ignoredCodes := []int{404, 409, 412}

	// Create the error handler function to be tested.
	isNonThreat := createUserErrorHandler(ignoredCodes)

	// Define the test cases in a table-driven format.
	tests := []struct {
		name          string
		inputError    error
		wantNonThreat bool
		wantErrOut    error
	}{
		{
			name:          "APIError_IgnoredStatusCode",
			inputError:    servus.ServiceError{HTTPCode: 404},
			wantNonThreat: true,
			wantErrOut:    servus.ServiceError{HTTPCode: 404},
		},
		{
			name:          "APIError_IgnoredStatusCode_BoundaryCase",
			inputError:    servus.ServiceError{HTTPCode: 412}, // A mock API error with another ignored code
			wantNonThreat: true,
			wantErrOut:    servus.ServiceError{HTTPCode: 412},
		},
		{
			name:          "APIError_NonIgnoredStatusCode",
			inputError:    servus.ServiceError{HTTPCode: 500}, // A mock API error with a non-ignored code
			wantNonThreat: false,
			wantErrOut:    servus.ServiceError{HTTPCode: 500},
		},
		{
			name:          "NonAPIError_GenericError",
			inputError:    errors.New("a generic error occurred"), // A standard, non-API error
			wantNonThreat: false,
			wantErrOut:    errors.New("a generic error occurred"),
		},
		{
			name:          "NonAPIError_AnotherType",
			inputError:    servus.ServiceError{Code: "some other kind of error"}, // Another mock error type
			wantNonThreat: false,
			wantErrOut:    servus.ServiceError{Code: "some other kind of error"},
		},
	}

	// Iterate through each test case and run the test.
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNonThreat, gotErrOut := isNonThreat(tt.inputError)

			// Check if the non-threat boolean matches the expected value.
			if gotNonThreat != tt.wantNonThreat {
				t.Errorf("isNonThreat() gotNonThreat = %v, want = %v", gotNonThreat, tt.wantNonThreat)
			}

			// Check if the returned error matches the input error.
			// The function should always return the same error it was given.
			if !reflect.DeepEqual(gotErrOut, tt.wantErrOut) {
				t.Errorf("isNonThreat() gotErrOut = %v, want = %v", gotErrOut, tt.wantErrOut)
			}
		})
	}
}

func Test_createIgnore4xxErrorHandler(t *testing.T) {
	isNonThreat := createIgnore4xxErrorHandler()
	tests := []struct {
		name          string
		inputError    error
		wantNonThreat bool
		wantErrOut    error
	}{
		{
			name:          "APIError_IgnoredStatusCode",
			inputError:    servus.ServiceError{HTTPCode: 404}, // A mock API error with an ignored code
			wantNonThreat: true,
			wantErrOut:    servus.ServiceError{HTTPCode: 404},
		},
		{
			name:          "APIError_IgnoredStatusCode_BoundaryCase",
			inputError:    servus.ServiceError{HTTPCode: 412}, // A mock API error with another ignored code
			wantNonThreat: true,
			wantErrOut:    servus.ServiceError{HTTPCode: 412},
		},
		{
			name:          "APIError_NonIgnoredStatusCode",
			inputError:    servus.ServiceError{HTTPCode: 500}, // A mock API error with a non-ignored code
			wantNonThreat: false,
			wantErrOut:    servus.ServiceError{HTTPCode: 500},
		},
		{
			name:          "NonAPIError_GenericError",
			inputError:    errors.New("a generic error occurred"), // A standard, non-API error
			wantNonThreat: false,
			wantErrOut:    errors.New("a generic error occurred"),
		},
		{
			name:          "NonAPIError_AnotherType",
			inputError:    servus.ServiceError{Code: "some other kind of error"}, // Another mock error type
			wantNonThreat: false,
			wantErrOut:    servus.ServiceError{Code: "some other kind of error"},
		},
	}

	// Iterate through each test case and run the test.
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNonThreat, gotErrOut := isNonThreat(tt.inputError)

			// Check if the non-threat boolean matches the expected value.
			if gotNonThreat != tt.wantNonThreat {
				t.Errorf("isNonThreat() gotNonThreat = %v, want = %v", gotNonThreat, tt.wantNonThreat)
			}

			// Check if the returned error matches the input error.
			// The function should always return the same error it was given.
			if !reflect.DeepEqual(gotErrOut, tt.wantErrOut) {
				t.Errorf("isNonThreat() gotErrOut = %v, want = %v", gotErrOut, tt.wantErrOut)
			}
		})
	}
}

func Test_is4xxStatusCode(t *testing.T) {
	type args struct {
		statusCode int
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "happy path - 400",
			args: args{
				statusCode: 400,
			},
			want: true,
		},
		{
			name: "happy path - 499",
			args: args{
				statusCode: 499,
			},
			want: true,
		},
		{
			name: "happy path - 4000",
			args: args{
				statusCode: 4000,
			},
			want: false,
		},
		{
			name: "happy path - 200",
			args: args{
				statusCode: 200,
			},
			want: false,
		},
		{
			name: "happy path - 40",
			args: args{
				statusCode: 40,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := is4xxStatusCode(tt.args.statusCode)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAccountDetails() = %v, want %v", got, tt.want)
			}
		})
	}
}
