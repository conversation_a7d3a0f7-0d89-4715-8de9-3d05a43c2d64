package klientopts

import (
	dogstatsd "github.com/DataDog/datadog-go/statsd"
	"gitlab.com/gx-regional/dakota/common/tracing"
	servus "gitlab.com/gx-regional/dakota/servus/v2"
	"gitlab.com/gx-regional/dakota/servus/v2/slog"
	"gitlab.com/gx-regional/dakota/servus/v2/statsd"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker"
)

func CreateKlientOptionsV2(serviceName string, endpointLevelCBSetting *external.EndpointLevelCBSetting, tracer tracing.Tracer, logger slog.YallLogger, withHealthCheck, propagateCommonHeaders bool, statsClient statsd.Client, dogStatsDClient dogstatsd.ClientInterface) []klient.Option {
	opts := CreateKlientOptions(serviceName, endpointLevelCBSetting, tracer, logger, withHealthCheck, propagateCommonHeaders, statsClient)
	if _, ok := dogStatsDClient.(*dogstatsd.Client); ok {
		opts = append(opts, klient.WithStatsDForCB(dogStatsDClient.(*dogstatsd.Client)))
	}
	return opts
}

func CreateKlientOptions(serviceName string, endpointLevelCBSetting *external.EndpointLevelCBSetting, tracer tracing.Tracer, logger slog.YallLogger, withHealthCheck, propagateCommonHeaders bool, statsClient statsd.Client) []klient.Option {
	opts := make([]klient.Option, 0)
	if serviceName != "" {
		opts = append(opts, klient.WithServiceName(serviceName))
	}

	if statsClient != nil {
		opts = append(opts, klient.WithStatsDClient(statsClient))
	}

	if endpointLevelCBSetting != nil {
		opts = append(opts, klient.WithCircuitConfig(endpointLevelCBSetting.CircuitSetting))

		if len(endpointLevelCBSetting.IgnoredHTTPCode) > 0 {
			userErrorHandler := createUserErrorHandler(endpointLevelCBSetting.IgnoredHTTPCode)
			opts = append(opts, klient.WithCircuitOptions([]circuitbreaker.Option{
				circuitbreaker.WithUserErrorHandler(userErrorHandler),
			}))
		} else if endpointLevelCBSetting.Ignore4xxErrorCode {
			opts = append(opts, klient.WithCircuitOptions([]circuitbreaker.Option{
				circuitbreaker.WithUserErrorHandler(createIgnore4xxErrorHandler()),
			}))
		}
	}

	if tracer != nil {
		opts = append(opts, klient.WithTracing(tracer))
	}

	if logger != nil {
		opts = append(opts, klient.WithLogger(logger))
	}

	if !withHealthCheck {
		opts = append(opts, klient.WithoutHealthCheck())
	}

	if propagateCommonHeaders {
		opts = append(opts, klient.PropagateCommonHeaders())
	}

	return opts
}

func createUserErrorHandler(ignoredHTTPCode []int) circuitbreaker.IsNonThreatErr {
	ignoredHTTPCodeMap := make(map[int]bool)

	for _, code := range ignoredHTTPCode {
		ignoredHTTPCodeMap[code] = true
	}

	return func(err error) (nonThreat bool, errOut error) {
		if e, ok := err.(servus.APIError); ok {
			httpCode, _ := e.Info()
			if ignoredHTTPCodeMap[httpCode] {
				return true, err
			}
		}
		return false, err
	}
}

func createIgnore4xxErrorHandler() circuitbreaker.IsNonThreatErr {
	return func(err error) (nonThreat bool, errOut error) {
		if e, ok := err.(servus.APIError); ok {
			httpCode, _ := e.Info()
			if is4xxStatusCode(httpCode) {
				return true, err
			}
		}
		return false, err
	}
}

func is4xxStatusCode(statusCode int) bool {
	return statusCode >= 400 && statusCode <= 499
}
