package hermes

import (
	"context"

	"gitlab.com/gx-regional/dakota/common/tracing"
	api "gitlab.com/gx-regional/dbmy/hermes/api"
	"gitlab.com/gx-regional/dbmy/hermes/api/client"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"
	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	logTag = "external.hermes"
)

// DbmyClient - defines dbmy hermes client
type DbmyClient struct {
	hermesClient api.Hermes
}

// NewDbmyClient instantiates a new Client.
func NewDbmyClient(conf *external.ServiceConfig, tracer tracing.Tracer) (*DbmyClient, error) {
	hermesClient, err := client.NewHermesClient(conf.BaseURL, klient.WithServiceName("hermes"), klient.WithTracing(tracer))
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		hermesClient: hermesClient,
	}, nil
}

// NewDbmyClientWithOptions instantiates a new Client with Options.
func NewDbmyClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (*DbmyClient, error) {
	hermesClient, err := client.NewHermesClient(conf.BaseURL, options...)
	if err != nil {
		return nil, err
	}
	return &DbmyClient{
		hermesClient: hermesClient,
	}, nil
}

func (c *DbmyClient) GetDocuments(ctx context.Context, req *GetDocumentsRequest) (*GetDocumentsResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("UserID", req.UserID), slog.CustomTag("ProfileID", req.ProfileID))
	
	apiRequest := &api.GetDocumentsRequest{
		DocumentIds: req.DocumentIDs,
		UserId:      req.UserID,
		ProfileId:   req.ProfileID,
	}
	
	apiResponse, err := c.hermesClient.GetDocuments(ctx, apiRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "HermesClient.GetDocuments error", slog.Error(err))
		return nil, err
	}

	response := &GetDocumentsResponse{
		Documents: convertAPIDocumentsToDTO(apiResponse.Documents),
	}

	return response, nil
}

func (c *DbmyClient) GetDocumentsStatus(ctx context.Context, req *GetDocumentsStatusRequest) (*GetDocumentsStatusResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("UserID", req.UserID), slog.CustomTag("ProfileID", req.ProfileID))
	
	apiRequest := &api.GetDocumentsStatusRequest{
		DocumentIds: req.DocumentIDs,
		UserId:      req.UserID,
		ProfileId:   req.ProfileID,
	}
	
	apiResponse, err := c.hermesClient.GetDocumentsStatus(ctx, apiRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "HermesClient.GetDocumentsStatus error", slog.Error(err))
		return nil, err
	}

	response := &GetDocumentsStatusResponse{
		DocumentStatuses: convertAPIDocumentStatusesToDTO(apiResponse.DocumentStatuses),
	}

	return response, nil
}

func (c *DbmyClient) GetUploadURL(ctx context.Context, req *GetUploadURLRequest) (*GetUploadURLResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("UserID", req.UserID), slog.CustomTag("ProfileID", req.ProfileID))
	
	apiRequest := &api.GetUploadURLRequest{
		DocumentType: req.DocumentType,
		FileType:     req.FileType,
		UserId:       req.UserID,
		ProfileId:    req.ProfileID,
	}
	
	apiResponse, err := c.hermesClient.GetUploadURL(ctx, apiRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "HermesClient.GetUploadURL error", slog.Error(err))
		return nil, err
	}

	response := &GetUploadURLResponse{
		UploadURL:   apiResponse.UploadUrl,
		DocumentID:  apiResponse.DocumentId,
		ExpiresAt:   apiResponse.ExpiresAt,
		UploadToken: apiResponse.UploadToken,
	}

	return response, nil
}

func (c *DbmyClient) GetUploadURLV2(ctx context.Context, req *GetUploadURLRequest) (*GetUploadURLResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("UserID", req.UserID), slog.CustomTag("ProfileID", req.ProfileID))
	
	apiRequest := &api.GetUploadURLRequest{
		DocumentType: req.DocumentType,
		FileType:     req.FileType,
		UserId:       req.UserID,
		ProfileId:    req.ProfileID,
	}
	
	apiResponse, err := c.hermesClient.GetUploadURLV2(ctx, apiRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "HermesClient.GetUploadURLV2 error", slog.Error(err))
		return nil, err
	}

	response := &GetUploadURLResponse{
		UploadURL:   apiResponse.UploadUrl,
		DocumentID:  apiResponse.DocumentId,
		ExpiresAt:   apiResponse.ExpiresAt,
		UploadToken: apiResponse.UploadToken,
	}

	return response, nil
}

func (c *DbmyClient) UpdateDocumentStatus(ctx context.Context, req *UpdateDocumentStatusRequest) error {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("UserID", req.UserID), slog.CustomTag("ProfileID", req.ProfileID), slog.CustomTag("DocumentID", req.DocumentID))
	
	apiRequest := &api.UpdateDocumentStatusRequest{
		DocumentId: req.DocumentID,
		Status:     req.Status,
		UserId:     req.UserID,
		ProfileId:  req.ProfileID,
	}
	
	err := c.hermesClient.UpdateDocumentStatus(ctx, apiRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "HermesClient.UpdateDocumentStatus error", slog.Error(err))
		return err
	}

	return nil
}

func (c *DbmyClient) UploadDocument(ctx context.Context, req *UploadDocumentRequest) (*UploadDocumentResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("UserID", req.UserID), slog.CustomTag("ProfileID", req.ProfileID))
	
	apiRequest := &api.UploadDocumentRequest{
		DocumentType: req.DocumentType,
		FileContent:  req.FileContent,
		FileName:     req.FileName,
		FileType:     req.FileType,
		UserId:       req.UserID,
		ProfileId:    req.ProfileID,
	}
	
	apiResponse, err := c.hermesClient.UploadDocument(ctx, apiRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "HermesClient.UploadDocument error", slog.Error(err))
		return nil, err
	}

	response := &UploadDocumentResponse{
		DocumentID: apiResponse.DocumentId,
		Status:     apiResponse.Status,
		UploadedAt: apiResponse.UploadedAt,
	}

	return response, nil
}

func (c *DbmyClient) UploadDocumentV2(ctx context.Context, req *UploadDocumentRequest) (*UploadDocumentResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("UserID", req.UserID), slog.CustomTag("ProfileID", req.ProfileID))
	
	apiRequest := &api.UploadDocumentRequest{
		DocumentType: req.DocumentType,
		FileContent:  req.FileContent,
		FileName:     req.FileName,
		FileType:     req.FileType,
		UserId:       req.UserID,
		ProfileId:    req.ProfileID,
	}
	
	apiResponse, err := c.hermesClient.UploadDocumentV2(ctx, apiRequest)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "HermesClient.UploadDocumentV2 error", slog.Error(err))
		return nil, err
	}

	response := &UploadDocumentResponse{
		DocumentID: apiResponse.DocumentId,
		Status:     apiResponse.Status,
		UploadedAt: apiResponse.UploadedAt,
	}

	return response, nil
}

// Helper functions to convert API types to DTO types
func convertAPIDocumentsToDTO(apiDocuments []*api.Document) []Document {
	documents := make([]Document, len(apiDocuments))
	for i, apiDoc := range apiDocuments {
		documents[i] = Document{
			ID:           apiDoc.Id,
			Type:         apiDoc.Type,
			Status:       apiDoc.Status,
			FileName:     apiDoc.FileName,
			FileType:     apiDoc.FileType,
			FileSize:     apiDoc.FileSize,
			UploadedAt:   apiDoc.UploadedAt.AsTime(),
			UpdatedAt:    apiDoc.UpdatedAt.AsTime(),
			UserID:       apiDoc.UserId,
			ProfileID:    apiDoc.ProfileId,
			DownloadURL:  apiDoc.DownloadUrl,
			ThumbnailURL: apiDoc.ThumbnailUrl,
		}
	}
	return documents
}

func convertAPIDocumentStatusesToDTO(apiStatuses []*api.DocumentStatus) []DocumentStatus {
	statuses := make([]DocumentStatus, len(apiStatuses))
	for i, apiStatus := range apiStatuses {
		statuses[i] = DocumentStatus{
			DocumentID: apiStatus.DocumentId,
			Status:     apiStatus.Status,
			UpdatedAt:  apiStatus.UpdatedAt,
		}
	}
	return statuses
}
