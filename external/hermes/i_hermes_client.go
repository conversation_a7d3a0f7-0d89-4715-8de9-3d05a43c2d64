// Package hermes provides the wrapper to return hermes service client based on service group name from config.
package hermes

import (
	"context"
	"errors"

	"gitlab.com/gx-regional/dbmy/partner-integration/common"
	"gitlab.com/gx-regional/dbmy/partner-integration/external"

	"gitlab.myteksi.net/dakota/klient"
)

//go:generate mockery --name=IHermesClient --inpackage --case=underscore
type IHermesClient interface {
	GetDocuments(ctx context.Context, req *GetDocumentsRequest) (*GetDocumentsResponse, error)
	GetDocumentsStatus(ctx context.Context, req *GetDocumentsStatusRequest) (*GetDocumentsStatusResponse, error)
	GetUploadURL(ctx context.Context, req *GetUploadURLRequest) (*GetUploadURLResponse, error)
	GetUploadURLV2(ctx context.Context, req *GetUploadURLRequest) (*GetUploadURLResponse, error)
	UpdateDocumentStatus(ctx context.Context, req *UpdateDocumentStatusRequest) error
	UploadDocument(ctx context.Context, req *UploadDocumentRequest) (*UploadDocumentResponse, error)
	UploadDocumentV2(ctx context.Context, req *UploadDocumentRequest) (*UploadDocumentResponse, error)
}

// NewClientWithOptions instantiates implementation of IHermesClient based on service group name
func NewClientWithOptions(conf *external.ServiceConfig, options ...klient.Option) (IHermesClient, error) {
	if conf.GroupName == common.GroupNameDBMY {
		return NewDbmyClientWithOptions(conf, options...)
	}
	return nil, errors.New("invalid value for hermes.groupName")
}
