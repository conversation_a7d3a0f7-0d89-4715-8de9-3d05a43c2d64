package external

import (
	"fmt"

	"gitlab.com/gx-regional/dakota/servus/v2"
)

type C<PERSON><PERSON><PERSON> string

const (
	// AccountServiceClient<PERSON><PERSON> declare for account-service client key
	AccountServiceClientKey ClientKey = "accountService"

	// IdentityExperienceClientKey ...
	IdentityExperienceClientKey ClientKey = "identityExperience"

	// GrabIDClientKey declare for grab-id client key
	GrabIDClientKey ClientKey = "grabID"

	// CustomerMasterClientKey ...
	CustomerMasterClientKey ClientKey = "customerMaster"

	// PartnerpayEngineClientKey ...
	PartnerpayEngineClientKey ClientKey = "partnerpayEngine"

	// ApplicationServiceClientKey ...
	ApplicationServiceClientKey ClientKey = "applicationService"

	// LoanExpClientKey ...
	LoanExpClientKey ClientKey = "loanExp"
)

// RequiredClientsConfig ...
type RequiredClientsConfig struct {
	RequiredClients *[]string `json:"requiredClients"`
}

type RequiredClients struct {
	clientFnMap map[ClientKey]interface{}
}

func NewClientsInitializer(clientFnMap map[ClientKey]interface{}) *RequiredClients {
	return &RequiredClients{
		clientFnMap: clientFnMap,
	}
}

func (rc *RequiredClients) Register(app *servus.Application, requiredClients *[]string) {
	if requiredClients == nil {
		panic(fmt.Errorf("requiredClients is missing in config"))
	}
	for _, key := range *requiredClients {
		if val, ok := rc.clientFnMap[ClientKey(key)]; ok {
			app.MustRegister("client."+key, val)
			continue
		}
		panic(fmt.Errorf("unable to find client key %v from map", key))
	}
}
