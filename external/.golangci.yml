# yaml-language-server: $schema=https://golangci-lint.run/jsonschema/golangci.jsonschema.json

# The version of the configuration format. Required for new versions.
version: "2"

# Options for analysis running.
run:
  # Timeout for analysis, e.g., 30s, 5m. Default is 5m.
  timeout: 30m

  # Include test files in analysis. Default is true.
  tests: true

# Output configuration options.
output:
  # Controls the output format. `colored-line-number` is the default and recommended.
  # Other formats: json, tab, checkstyle, code-climate, etc.
  formats:
    text:
      colors: true
      print-issued-lines: true
      print-linter-name: true


# Settings for formatters are in their own top-level section.
formatters:
  settings:
    gofmt:
      # Simplify code: gofmt with `-s` option. True by default.
      simplify: true
    goimports:
      # Comma-separated list of prefixes for local packages.
      local-prefixes: github.com/org/project

# All linter settings are now under the 'linters' section.
linters:
  default: none
  enable:
    - bodyclose
    - dogsled
    - dupl
    - errcheck
    - funlen
    - gocognit
    - goconst
    - gocritic
    - gosec
    - govet
    - ineffassign
    - nakedret
    # 'typecheck' is no longer a linter but a core part of the runner. It's always on.
    # 'gofmt' and 'goimports' were moved to the formatters section.
    - unconvert
    - unparam
    - unused
    - whitespace
#    - wsl

  exclusions:
    # Excluding configuration per-path, per-linter, per-text and per-source.
    rules:
      # Exclude some linters from running on tests files.
      - path: _test\.go$
        linters:
          - gocyclo
          - errcheck
          - dupl
          - gosec
          - funlen
      - linters:
          - lll
        source: "^//go:generate "

# all available settings of specific linters
linters-settings:
  funlen:
    lines: 200
    statements: 40
