package external

import (
	"context"
	"reflect"
	"testing"

	activeprofile "gitlab.com/gx-regional/dbmy/common/active-profile/v2"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
)

func TestAddCommonHeaders(t *testing.T) {
	type args struct {
		ctx       context.Context
		serviceID string
		userID    string
		profileID string
	}
	type want struct {
		serviceID   string
		userID      string
		profileID   string
		profileType string
	}
	tests := []struct {
		name string
		args args
		want want
	}{
		{
			name: "with all parameters",
			args: args{
				ctx:       context.Background(),
				serviceID: "serviceID",
				userID:    "userID",
				profileID: "BIF0000000",
			},
			want: want{
				serviceID:   "serviceID",
				userID:      "userID",
				profileID:   "BIF0000000",
				profileType: string(activeprofile.BUSINESS),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotCtx := AddCommonHeaders(tt.args.ctx, tt.args.serviceID, tt.args.userID, tt.args.profileID)
			got := want{
				serviceID:   commonCtx.GetHTTPHeader(gotCtx, commonCtx.HeaderXServiceID),
				userID:      commonCtx.GetHTTPHeader(gotCtx, commonCtx.HeaderXUserID),
				profileID:   commonCtx.GetHTTPHeader(gotCtx, commonCtx.HeaderXProfileID),
				profileType: commonCtx.GetHTTPHeader(gotCtx, commonCtx.HeaderXProfileType),
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddCommonHeaders() = %v, want %v", got, tt.want)
			}
		})
	}
}
