module gitlab.com/gx-regional/dbmy/partner-integration/external

go 1.24.1

replace (
	gitlab.com/gx-regional/dbmy/partner-integration/common => ../common
	gitlab.myteksi.net/dakota/common => gitlab.com/gx-regional/dakota/common.git v1.0.5
	gitlab.myteksi.net/dakota/common/context => gitlab.com/gx-regional/dakota/common.git/context v0.17.0
	gitlab.myteksi.net/dakota/common/countries => gitlab.com/gx-regional/dakota/common.git/countries v0.0.0-20231129045548-36b0a314566c // indirect
	gitlab.myteksi.net/dakota/common/env-injection => gitlab.com/gx-regional/dakota/common.git/env-injection v1.0.0 // indirect
	gitlab.myteksi.net/dakota/common/redis => gitlab.com/gx-regional/dakota/common.git/redis v0.19.0 // indirect
	gitlab.myteksi.net/dakota/common/secrets-injection => gitlab.com/gx-regional/dakota/common.git/secrets-injection v1.0.3 // indirect
	gitlab.myteksi.net/dakota/common/servicename => gitlab.com/gx-regional/dakota/common.git/servicename v1.55.0
	gitlab.myteksi.net/dakota/common/tracing => gitlab.com/gx-regional/dakota/common.git/tracing v1.10.0
	gitlab.myteksi.net/dakota/common/transaction-code => gitlab.com/gx-regional/dakota/common.git/transaction-code v1.52.0 // indirect
	gitlab.myteksi.net/dakota/gaia => gitlab.com/gx-regional/dakota/gaia.git v0.1.1 // indirect
	gitlab.myteksi.net/dakota/klient => gitlab.com/gx-regional/dakota/klient.git v1.25.1
	gitlab.myteksi.net/dakota/servus/v2 => gitlab.com/gx-regional/dakota/servus.git/v2 v2.54.0
	gitlab.myteksi.net/dbmy/customer-experience/api => gitlab.com/gx-regional/dbmy/customer-experience.git/api v1.57.7
	gitlab.myteksi.net/dbmy/customer-master/api/v2 => gitlab.com/gx-regional/dbmy/customer-master/api/v2 v2.52.1-dbmy
)

require (
	github.com/DataDog/datadog-go v4.8.3+incompatible
	github.com/json-iterator/go v1.1.12
	github.com/myteksi/hystrix-go v1.1.3
	github.com/stretchr/testify v1.10.0
	gitlab.com/gx-regional/dakota/common/context v0.18.0
	gitlab.com/gx-regional/dakota/common/tracing v1.13.0
	gitlab.com/gx-regional/dakota/klient v1.28.0
	gitlab.com/gx-regional/dakota/lending/loan-exp/api v1.147.0
	gitlab.com/gx-regional/dakota/payment/partnerpay-engine/api v1.62.0
	gitlab.com/gx-regional/dakota/servus/v2 v2.60.0
	gitlab.com/gx-regional/dbmy/application-service/api v1.27.9-0.**************-cedad54510d9
	gitlab.com/gx-regional/dbmy/common/active-profile/v2 v2.2.0
	gitlab.com/gx-regional/dbmy/core-banking/account-service/api v1.79.0-dbmy
	gitlab.com/gx-regional/dbmy/customer-experience/api v1.57.7
	gitlab.com/gx-regional/dbmy/customer-master/api/v2 v2.62.4-dbmy
	gitlab.com/gx-regional/dbmy/identity-experience/api v1.76.0-dbmy
	gitlab.com/gx-regional/dbmy/partner-integration/common v0.0.0-**************-************
	gitlab.myteksi.net/dakota/common/context v0.17.0
	gitlab.myteksi.net/dakota/common/servicename v1.55.0
	gitlab.myteksi.net/dakota/klient v1.25.1
	gitlab.myteksi.net/dakota/servus/v2 v2.54.0
	gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker v1.1.1
	gitlab.myteksi.net/gophers/go/commons/util/tags v1.1.7
)

require (
	github.com/DataDog/datadog-agent/pkg/obfuscate v0.36.1 // indirect
	github.com/DataDog/datadog-go/v5 v5.1.1 // indirect
	github.com/DataDog/sketches-go v1.4.1 // indirect
	github.com/Microsoft/go-winio v0.5.2 // indirect
	github.com/alecholmes/xfccparser v0.1.0 // indirect
	github.com/alecthomas/participle v0.4.1 // indirect
	github.com/aws/aws-sdk-go v1.55.7 // indirect
	github.com/cactus/go-statsd-client/v4 v4.0.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dgraph-io/ristretto v0.1.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/flosch/pongo2 v0.0.0-20200913210552-0d938eb266f3 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/garyburd/redigo v1.6.4 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-redis/redis v6.15.9+incompatible // indirect
	github.com/go-sql-driver/mysql v1.7.1 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/glog v1.2.5 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/gorilla/mux v1.8.0 // indirect
	github.com/gorilla/schema v1.4.1 // indirect
	github.com/grpc-ecosystem/grpc-opentracing v0.0.0-20170512040955-6c130eed1e29 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/julienschmidt/httprouter v1.3.0 // indirect
	github.com/lightstep/lightstep-tracer-common/golang/gogo v0.0.0-20210210170715-a8dfcb80d3a7 // indirect
	github.com/lightstep/lightstep-tracer-go v0.25.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20240513124658-fba389f38bae // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/myteksi/schema v0.0.0-20180214071320-149151f79a92 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/philhofer/fwd v1.1.2 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/power-devops/perfstat v0.0.0-20240221224432-82ca36839d55 // indirect
	github.com/pquerna/ffjson v0.0.0-20190930134022-aa0246cd15f7 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/redis/go-redis/v9 v9.7.3 // indirect
	github.com/reterVision/go-kinesis v0.0.0-20150928061512-c0f0783318c3 // indirect
	github.com/rogpeppe/go-internal v1.14.1 // indirect
	github.com/shirou/gopsutil/v3 v3.23.10 // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/showa-93/go-mask v0.6.1 // indirect
	github.com/smartystreets/goconvey v1.8.1 // indirect
	github.com/spiffe/go-spiffe/v2 v2.1.1 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/tinylib/msgp v1.1.8 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	gitlab.com/gx-regional/dakota/common/env-injection v1.2.0-migration // indirect
	gitlab.com/gx-regional/dakota/common/redis v0.29.2 // indirect
	gitlab.com/gx-regional/dakota/common/secrets-injection v1.1.0-migration // indirect
	gitlab.com/gx-regional/dakota/common/servicename v1.65.0 // indirect
	gitlab.com/gx-regional/dakota/common/slog v1.1.0 // indirect
	gitlab.com/gx-regional/dakota/common/statsd v1.1.0 // indirect
	gitlab.com/gx-regional/dakota/gaia v0.2.0-migration // indirect
	gitlab.myteksi.net/dakota/common/env-injection v1.0.0 // indirect
	gitlab.myteksi.net/dakota/common/redis v0.19.0 // indirect
	gitlab.myteksi.net/dakota/common/secrets-injection v1.0.3 // indirect
	gitlab.myteksi.net/dakota/common/tracing v1.10.0 // indirect
	gitlab.myteksi.net/dakota/gaia v0.1.1 // indirect
	gitlab.myteksi.net/dbmy/customer-master/api/v2 v2.53.0-dbmy // indirect
	gitlab.myteksi.net/gophers/go/commons/algo/cmap v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/data v1.0.11 // indirect
	gitlab.myteksi.net/gophers/go/commons/deprecation v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/aws/grabkinesis v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/config/conf v1.0.2 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/config/mode v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/encoding/grabjson v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/ldflags v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/internal/logdefaults v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/logging v1.1.7 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/timerlog v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/yall v1.0.18 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/monitor/statsd v1.0.10 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/network/iputil v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/redis/gredis v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/redis/gredismigrate v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/resilience/hystrix v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/server/swaggergen v1.0.4 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/time/grabtime v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/tracer v1.0.5 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/validate v1.1.4 // indirect
	gitlab.myteksi.net/gophers/go/spartan/lechuck v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/staples/gredis3 v1.0.8 // indirect
	gitlab.myteksi.net/gophers/go/staples/logging v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/staples/statsd v1.0.14 // indirect
	gitlab.myteksi.net/spartan/hystrix-go/v2 v2.0.1 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.26.0 // indirect
	golang.org/x/net v0.38.0 // indirect
	golang.org/x/sys v0.32.0 // indirect
	golang.org/x/text v0.24.0 // indirect
	golang.org/x/time v0.3.0 // indirect
	golang.org/x/xerrors v0.0.0-20220907171357-04be3eba64a2 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20241202173237-19429a94021a // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20241202173237-19429a94021a // indirect
	google.golang.org/grpc v1.70.0 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	gopkg.in/DataDog/dd-trace-go.v1 v1.38.1 // indirect
	gopkg.in/redsync.v1 v1.0.1 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
